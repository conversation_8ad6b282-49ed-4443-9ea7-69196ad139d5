/* ========================================
   MOBILE PRINT ONLY - SEPARATE FILE
   This file only affects mobile devices (max-width: 768px)
   Desktop remains completely unaffected
   ======================================== */

@media print and (max-width: 768px) {
    /* Force all hidden columns to be visible on mobile print */
    .dtr-hidden,
    .d-none,
    .d-sm-none,
    .d-md-none,
    .d-lg-none,
    .d-xl-none,
    .d-xxl-none {
        display: table-cell !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Mobile table optimization */
    .table {
        font-size: 8px !important;
        table-layout: fixed !important;
        width: 100% !important;
    }

    /* Compact all table cells for mobile */
    .table th,
    .table td {
        padding: 3px 2px !important;
        font-size: 7px !important;
        line-height: 1.2 !important;
        word-wrap: break-word !important;
        white-space: nowrap !important;
        border: 1px solid #ddd !important;
    }

    /* Sequential column - extra compact */
    .table th:first-child,
    .table td:first-child {
        width: 25px !important;
        max-width: 25px !important;
        min-width: 25px !important;
        font-size: 7px !important;
        padding: 2px 1px !important;
    }

    /* Header styling for mobile */
    .table thead th {
        font-size: 7px !important;
        padding: 3px 2px !important;
        font-weight: 600 !important;
    }

    /* Body styling for mobile */
    .table tbody td {
        font-size: 7px !important;
        padding: 3px 2px !important;
    }

    /* Footer/totals styling for mobile */
    .table tfoot td {
        font-size: 7px !important;
        padding: 3px 2px !important;
        font-weight: 700 !important;
    }

    /* Hide DataTables controls on mobile print */
    .dataTables_length,
    .dataTables_filter,
    .dataTables_info,
    .dataTables_paginate,
    .dataTables_processing {
        display: none !important;
    }

    /* Force DataTables wrapper to show all content */
    .dataTables_wrapper {
        overflow: visible !important;
        width: 100% !important;
    }

    /* Ensure responsive columns are visible */
    .dataTables_wrapper .dtr-inline.collapsed > tbody > tr > td.child,
    .dataTables_wrapper .dtr-inline.collapsed > tbody > tr > th.child,
    .dataTables_wrapper .dtr-inline.collapsed > tbody > tr[role="row"] > td.child,
    .dataTables_wrapper .dtr-inline.collapsed > tbody > tr[role="row"] > th.child {
        display: none !important;
    }

    /* Force all columns to display as table cells */
    .dataTables_wrapper table.dataTable tbody tr td,
    .dataTables_wrapper table.dataTable tbody tr th {
        display: table-cell !important;
    }
}

/* Extra small mobile devices (480px and below) */
@media print and (max-width: 480px) {
    .table {
        font-size: 6px !important;
    }

    .table th,
    .table td {
        padding: 2px 1px !important;
        font-size: 6px !important;
    }

    .table th:first-child,
    .table td:first-child {
        width: 20px !important;
        max-width: 20px !important;
        min-width: 20px !important;
        font-size: 6px !important;
        padding: 1px !important;
    }

    .table thead th {
        font-size: 6px !important;
        padding: 2px 1px !important;
    }

    .table tbody td {
        font-size: 6px !important;
        padding: 2px 1px !important;
    }

    .table tfoot td {
        font-size: 6px !important;
        padding: 2px 1px !important;
    }
}
