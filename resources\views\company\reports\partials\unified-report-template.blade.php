{{-- 
    UNIFIED SOPHISTICATED BUSINESS REPORT TEMPLATE
    Extracted from sales_report.blade.php - Gold Standard Design
    Modern Corporate Standards with DataTables AJAX Architecture
    Following industry best practices for professional reports
--}}

<style>
    /* ========================================
       SOPHISTICATED BUSINESS REPORT DESIGN
       Sales Report - Modern Corporate Standards
       Following industry best practices for professional reports
       ======================================== */

    @media print {
        /* ========================================
           PREMIUM PAGE SETUP & TYPOGRAPHY
           Remove browser default headers/footers
           ======================================== */
        @page {
            size: A4 portrait;
            margin: 0;
            @top-center { content: ""; }
            @top-left { content: ""; }
            @top-right { content: ""; }
            @bottom-center { content: ""; }
            @bottom-left { content: ""; }
            @bottom-right { content: ""; }
        }

        /* Remove top spacing from body and printable section */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }

        #printable-section {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            color: #2c3e50;
            background: #ffffff;
            line-height: 1.6;
        }

        /* ========================================
           CONTENT VISIBILITY CONTROL
           ======================================== */
        body * {
            visibility: hidden;
        }

        #printable-section,
        #printable-section * {
            visibility: visible;
        }

        #printable-section {
            position: relative;
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
            color: #2c3e50;
            overflow: visible;
        }
        
        /* ========================================
           SOPHISTICATED CORPORATE HEADER DESIGN
           Modern business report standards with elegant typography
           Dynamic company information populated by JavaScript
           ======================================== */

        /* Company Information Header - PRINT ONLY */
        .company-header-box {
            display: block !important; /* Show when printing */
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            line-height: 2.2;
            color: #2c3e50;
            font-size: 10px;
            font-weight: 400;
            margin-top: -80px;
            padding: 25px 0 25px 160px; /* Increased left padding for logo space */
            background: transparent;
            border: none;
            border-radius: 8px;
            box-shadow: none;
            position: relative;
            page-break-inside: avoid;
            overflow: hidden; /* Clearfix for floated elements */
        }

        /* Header Divider Line */
        .header-divider {
            display: block !important;
            width: 100%;
            height: 1px;
            background: #2c3e50;
            page-break-inside: avoid;
        }

        .company-header-box::after {
            content: "";
            display: table;
            clear: both;
        }

        /* Report Title and Date - HTML Div Element */
        #printable-section {
            position: relative;
            width: 100%;
            margin: 0;
            padding: 0;
        }

        /* Company Info Section - Right side of header box */
        .company-info-section {
            display: block;
            text-align: right;
            direction: rtl;
            float: right;
            width: 40%;
        }

        /* Report Title Section - Center of header box */
        .report-title-box {
            display: block !important; /* Show when printing */
            text-align: center;
            float: left;
            width: 35%;
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            font-size: 14px;
            font-weight: 600;
            margin-top: 10px;
            padding: 15px 10px;
            background: transparent;
            border: none;
            border-radius: 6px;
            margin-left: 25%;
        }

        .report-title {
            font-size: 16px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            color: #34495e;
        }

        .report-date {
            font-size: 12px;
            font-weight: 400;
            color: #7f8c8d;
        }

        /* Company Name Emphasis */
        .company-name {
            font-size: 12px;
            font-weight: 600;
            color: #34495e;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Elegant Company Logo - Left side of header box */
        @media print {
            .print-company-logo {
                display: block !important;
                position: absolute;
                top: 25px;
                left: 0;
                max-width: 110px;
                max-height: 110px;
                object-fit: contain;
                border: none;
                padding: 8px;
                box-shadow: 0 2px 6px rgba(52, 73, 94, 0.15);
                z-index: 10;
            }
        }

        /* Hide logo on screen, show only in print */
        .print-company-logo {
            display: none;
        }

        /* ========================================
           HIDE NON-PRINTABLE ELEMENTS
           ======================================== */
        .btn, .card-toolbar, .filter-section,
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate,
        .dataTables_wrapper .dataTables_processing,
        .fv-row, .form-control, .form-label {
            display: none !important;
        }

        /* ========================================
           SOPHISTICATED TABLE DESIGN - DESKTOP ONLY
           Modern corporate styling with elegant color scheme
           ======================================== */
        @media print and (min-width: 769px) {
            .table {
                border-collapse: collapse !important;
                width: 100% !important;
                margin-top: 20px !important; /* Normal spacing since we're using HTML elements */
                font-size: 10px !important;
                background: #ffffff !important;
                border: 1px solid #bdc3c7 !important;
                border-radius: 6px !important;
                overflow: hidden !important;
                box-shadow: 0 3px 12px rgba(52, 73, 94, 0.1) !important;
            }
        }

        /* Default table styles for all devices */
        .table {
            border-collapse: collapse !important;
            width: 100% !important;
            margin-top: 20px !important;
            background: #ffffff !important;
            border: 1px solid #bdc3c7 !important;
        }

        /* Desktop Table Header - Elegant styling */
        @media print and (min-width: 769px) {
            .table thead th {
                background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
                color: #ffffff !important;
                font-weight: 600 !important;
                text-align: center !important;
                padding: 14px 10px !important;
                border: none !important;
                border-bottom: 2px solid #2c3e50 !important;
                font-size: 11px !important;
                text-transform: uppercase;
                letter-spacing: 0.8px;
                font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            }

            /* Professional Table Body */
            .table tbody td {
                padding: 12px 10px !important;
                border: none !important;
                border-bottom: 1px solid #ecf0f1 !important;
                text-align: center !important;
                vertical-align: middle !important;
                background: #ffffff !important;
                color: #2c3e50 !important;
                font-weight: 400 !important;
                font-size: 10px !important;
                transition: background-color 0.2s ease !important;
            }
        }

        /* Basic table styling for all devices */
        .table thead th {
            background: #34495e !important;
            color: #ffffff !important;
            font-weight: 600 !important;
            text-align: center !important;
            padding: 8px 6px !important;
            border-bottom: 1px solid #2c3e50 !important;
            font-size: 9px !important;
        }

        .table tbody td {
            padding: 8px 6px !important;
            border-bottom: 1px solid #ecf0f1 !important;
            text-align: center !important;
            vertical-align: middle !important;
            background: #ffffff !important;
            color: #2c3e50 !important;
            font-size: 9px !important;
        }

        /* Desktop Row Alternation and Footer */
        @media print and (min-width: 769px) {
            .table tbody tr:nth-child(even) {
                background: #f8f9fa !important;
            }

            .table tbody tr:hover {
                background: #e8f4f8 !important;
            }

            /* Sophisticated Footer/Totals */
            .table tfoot td {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
                font-weight: 700 !important;
                color: #ffffff !important;
                padding: 16px 10px !important;
                border: none !important;
                border-top: 3px solid #2c3e50 !important;
                text-align: center !important;
                font-size: 11px !important;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
        }

        /* Basic footer styling for all devices */
        .table tfoot td {
            background: #2c3e50 !important;
            color: #ffffff !important;
            font-weight: 700 !important;
            padding: 8px 6px !important;
            border-top: 2px solid #2c3e50 !important;
            text-align: center !important;
            font-size: 9px !important;
        }

        /* Distinguished Sequential Column */
        .table tbody td:first-child {
            background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%) !important;
            font-weight: 600 !important;
            color: #34495e !important;
            width: 60px !important;
            text-align: center !important;
            border-right: 2px solid #bdc3c7 !important;
            font-size: 10px !important;
        }

        /* Enhanced Totals Row - Match Table Cells */
        .table .totals-row td {
            background: linear-gradient(135deg,  0%, #34495e 100%) !important;
            color: #ffffff !important;
            font-weight: 700 !important;
            font-size: 11px !important;
            padding: 16px 10px !important;
            border: none !important;
            border-top: 3px solid #2c3e50 !important;
            text-align: center !important;
            vertical-align: middle !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
        }

        /* Specific styling for each totals row cell */
        .table .totals-row .total-sequence {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
            width: 60px !important;
            border-right: 2px solid #1a252f !important;
            text-align: center !important;
        }

        .table .totals-row .total-date,
        .table .totals-row .total-details,
        .table .totals-row .total-account {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
        }

        .table .totals-row .total-debtor,
        .table .totals-row .total-creditor {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
            font-weight: 700 !important;
        }

        /* ========================================
           SOPHISTICATED VISUAL ENHANCEMENTS
           ======================================== */

        /* Amount Columns Styling */
        .table tbody td.debtor-column,
        .table tbody td.creditor-column {
            font-weight: 600 !important;
            background: #f8fff9 !important;
        }

        /* Negative amounts styling */
        .table tbody td.negative-amount {
            color: #e74c3c !important;
            background: #fdf2f2 !important;
            border-left: 3px solid #e74c3c !important;
        }

        /* ========================================
           SOPHISTICATED PAGE LAYOUT & BREAKS
           ======================================== */

        /* Premium Header Protection */
        .print-company-logo,
        #printable-section::before {
            page-break-inside: avoid;
            page-break-after: avoid;
            orphans: 3;
            widows: 3;
        }

        /* Table Structure Integrity */
        .table {
            page-break-inside: auto;
            margin-bottom: 25px;
        }

        .table thead {
            page-break-inside: avoid;
            page-break-after: avoid;
            display: table-header-group;
        }

        .table tbody tr {
            page-break-inside: avoid;
            page-break-after: auto;
            orphans: 2;
            widows: 2;
        }

        /* Keep standard table footer behavior */
        .table tfoot {
            display: table-footer-group;
            page-break-inside: avoid;
            page-break-before: avoid;
        }

        .totals-row {
            page-break-inside: avoid;
            page-break-before: avoid;
            display: table-row;
        }

        /* Use CSS to prevent footer repetition without changing table structure */
        @media print {
            /* Prevent automatic footer repetition on each page */
            .table tfoot {
                display: table-footer-group;
                break-inside: avoid;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .print-only-totals {
                display: table-footer-group !important;
                visibility: visible !important;
            }
        }

        /* ========================================
           PROFESSIONAL SPACING & TYPOGRAPHY
           ======================================== */

        /* Enhanced Readability */
        .table tbody td {
            line-height: 1.5 !important;
        }

        /* Professional Text Alignment */
        .text-start {
            text-align: center !important;
        }

        /* Hide Non-Essential Elements */
        .btn, .card-toolbar, .filter-section,
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate,
        .dataTables_wrapper .dataTables_processing,
        .fv-row, .form-control, .form-label {
            display: none !important;
        }

        /* ========================================
           ENHANCED TOTALS DISPLAY
           ======================================== */
        .totals-row {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            border-top: 3px solid #2c3e50 !important;
            font-weight: 700 !important;
        }

        .totals-row td {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
            font-weight: 700 !important;
            font-size: 11px !important;
            padding: 16px 10px !important;
            border: none !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Ensure totals row is properly styled */
        .totals-row td {
            border: 1px solid #dee2e6 !important;
            padding: 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
            font-size: 13px !important;
            font-weight: 600 !important;
            background-color: #f8f9fa !important;
        }

        /* Show totals only in print */
        .print-only-totals {
            display: table-footer-group !important;
            visibility: visible !important;
        }

        .print-only-totals tr {
            display: table-row !important;
            visibility: visible !important;
        }

        .print-only-totals td {
            display: table-cell !important;
            visibility: visible !important;
            border: 1px solid #dee2e6 !important;
            padding: 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
            font-size: 13px !important;
            font-weight: 700 !important;
        }

        /* ========================================
           MOBILE PRINT OPTIMIZATION - MOBILE ONLY
           Desktop version remains completely unchanged
           ======================================== */

        /* Mobile Print - Force All Columns Visible */
        @media print and (max-width: 768px) {
            /* Override DataTables responsive hiding on mobile */
            .dtr-hidden,
            .d-none,
            .d-sm-none,
            .d-md-none,
            .d-lg-none,
            .d-xl-none,
            .d-xxl-none {
                display: table-cell !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* Force table to show all columns on mobile */
            .table {
                width: 100% !important;
                table-layout: fixed !important;
                border-collapse: collapse !important;
                font-size: 8px !important;
                margin-top: 20px !important;
                background: #ffffff !important;
                border: 1px solid #bdc3c7 !important;
            }

            /* Mobile column optimization */
            .table th,
            .table td {
                padding: 3px 2px !important;
                font-size: 7px !important;
                line-height: 1.2 !important;
                border: 1px solid #ddd !important;
                text-align: center !important;
                vertical-align: middle !important;
                word-wrap: break-word !important;
                overflow: visible !important;
                white-space: nowrap !important;
                display: table-cell !important;
                visibility: visible !important;
            }

            /* Sequential column - always visible and compact */
            .table th:first-child,
            .table td:first-child {
                width: 25px !important;
                max-width: 25px !important;
                min-width: 25px !important;
                padding: 2px 1px !important;
                font-size: 7px !important;
                text-align: center !important;
                background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%) !important;
                font-weight: 600 !important;
                color: #34495e !important;
                border-right: 2px solid #bdc3c7 !important;
            }

            /* Header styling for mobile */
            .table thead th {
                background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
                color: #ffffff !important;
                font-weight: 600 !important;
                font-size: 7px !important;
                padding: 3px 2px !important;
                border-bottom: 2px solid #2c3e50 !important;
            }

            /* Body styling for mobile */
            .table tbody td {
                background: #ffffff !important;
                color: #2c3e50 !important;
                font-weight: 400 !important;
                border-bottom: 1px solid #ecf0f1 !important;
            }

            /* Alternating rows for mobile */
            .table tbody tr:nth-child(even) {
                background: #f8f9fa !important;
            }

            .table tbody tr:nth-child(even) td {
                background: #f8f9fa !important;
            }

            /* Totals row mobile optimization */
            .table .totals-row td,
            .table tfoot td {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
                color: #ffffff !important;
                font-weight: 700 !important;
                font-size: 7px !important;
                padding: 3px 2px !important;
                border-top: 3px solid #2c3e50 !important;
            }

            /* Force DataTables wrapper to show all content */
            .dataTables_wrapper {
                overflow: visible !important;
                width: 100% !important;
            }

            /* Hide DataTables controls on mobile print */
            .dataTables_length,
            .dataTables_filter,
            .dataTables_info,
            .dataTables_paginate,
            .dataTables_processing {
                display: none !important;
            }
        }

        /* Extra Small Mobile Devices (480px and below) */
        @media print and (max-width: 480px) {
            .table {
                font-size: 6px !important;
            }

            .table th,
            .table td {
                padding: 2px 1px !important;
                font-size: 6px !important;
            }

            .table th:first-child,
            .table td:first-child {
                width: 20px !important;
                max-width: 20px !important;
                min-width: 20px !important;
                font-size: 6px !important;
            }

            .table thead th {
                font-size: 6px !important;
                padding: 2px 1px !important;
            }

            .table .totals-row td,
            .table tfoot td {
                font-size: 6px !important;
                padding: 2px 1px !important;
            }
        }

        /* ========================================
           FINAL POLISH & REFINEMENTS
           ======================================== */

        /* Ensure Color Accuracy */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

    }

    /* ========================================
       SCREEN VIEW - HIDE PRINT ELEMENTS
       ======================================== */
    @media screen {
        .company-header-box,
        .company-info-section,
        .report-title-box,
        .print-only-totals {
            display: none !important; /* Hidden in normal view */
        }
    }

    /* ========================================
       SCREEN VIEW - HIDE TOTALS ON SCREEN
       ======================================== */

    .print-only {
        display: none;
    }
    @media print {
        .print-only {
            display: table-row !important;
        }
        /* Style the totals row like the table header for print */
        .totals-row.print-only td {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #fff !important;
            font-weight: bold !important;
            font-size: 12px !important;
            border: 1px solid #2c3e50 !important;
            text-align: center !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }

</style>
