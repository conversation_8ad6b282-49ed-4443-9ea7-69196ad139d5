{{-- 
    UNIFIED SOPHISTICATED BUSINESS REPORT TEMPLATE
    Extracted from sales_report.blade.php - Gold Standard Design
    Modern Corporate Standards with DataTables AJAX Architecture
    Following industry best practices for professional reports
--}}

<style>
    /* ========================================
       SOPHISTICATED BUSINESS REPORT DESIGN
       Sales Report - Modern Corporate Standards
       Following industry best practices for professional reports
       ======================================== */

    @media print {
        /* ========================================
           PREMIUM PAGE SETUP & TYPOGRAPHY
           Remove browser default headers/footers
           ======================================== */
        @page {
            size: A4 portrait;
            margin: 0 !important;
            padding: 0 !important;
            @top-center { content: "" !important; }
            @top-left { content: "" !important; }
            @top-right { content: "" !important; }
            @bottom-center { content: "" !important; }
            @bottom-left { content: "" !important; }
            @bottom-right { content: "" !important; }
        }

        /* Complete browser header/footer removal */
        @page :first {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
        }

        @page :left {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        @page :right {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        /* Remove top spacing from body and printable section */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }

        #printable-section {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            color: #2c3e50;
            background: #ffffff;
            line-height: 1.6;
        }

        /* ========================================
           CONTENT VISIBILITY CONTROL
           ======================================== */
        body * {
            visibility: hidden;
        }

        #printable-section,
        #printable-section * {
            visibility: visible;
        }

        #printable-section {
            position: relative;
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
            color: #2c3e50;
            overflow: visible;
        }
        
        /* ========================================
           SOPHISTICATED CORPORATE HEADER DESIGN
           Modern business report standards with elegant typography
           Dynamic company information populated by JavaScript
           ======================================== */

        /* Company Information Header - PRINT ONLY */
        .company-header-box {
            display: block !important; /* Show when printing */
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            line-height: 2.2;
            color: #2c3e50;
            font-size: 10px;
            font-weight: 400;
            margin-top: -80px;
            padding: 25px 0 25px 160px; /* Increased left padding for logo space */
            background: transparent;
            border: none;
            border-radius: 8px;
            box-shadow: none;
            position: relative;
            page-break-inside: avoid;
            overflow: hidden; /* Clearfix for floated elements */
        }

        /* Header Divider Line */
        .header-divider {
            display: block !important;
            width: 100%;
            height: 1px;
            background: #2c3e50;
            page-break-inside: avoid;
        }

        .company-header-box::after {
            content: "";
            display: table;
            clear: both;
        }

        /* Report Title and Date - HTML Div Element */
        #printable-section {
            position: relative;
            width: 100%;
            margin: 0;
            padding: 0;
        }

        /* Company Info Section - Right side of header box */
        .company-info-section {
            display: block;
            text-align: right;
            direction: rtl;
            float: right;
            width: 40%;
        }

        /* Report Title Section - Center of header box */
        .report-title-box {
            display: block !important; /* Show when printing */
            text-align: center;
            float: left;
            width: 35%;
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            font-size: 14px;
            font-weight: 600;
            margin-top: 10px;
            padding: 15px 10px;
            background: transparent;
            border: none;
            border-radius: 6px;
            margin-left: 25%;
        }

        .report-title {
            font-size: 16px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            color: #34495e;
        }

        .report-date {
            font-size: 12px;
            font-weight: 400;
            color: #7f8c8d;
        }

        /* Company Name Emphasis */
        .company-name {
            font-size: 12px;
            font-weight: 600;
            color: #34495e;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Elegant Company Logo - Left side of header box */
        @media print {
            .print-company-logo {
                display: block !important;
                position: absolute;
                top: 25px;
                left: 0;
                max-width: 110px;
                max-height: 110px;
                object-fit: contain;
                border: none;
                padding: 8px;
                box-shadow: 0 2px 6px rgba(52, 73, 94, 0.15);
                z-index: 10;
            }
        }

        /* Hide logo on screen, show only in print */
        .print-company-logo {
            display: none;
        }

        /* ========================================
           HIDE NON-PRINTABLE ELEMENTS
           ======================================== */
        .btn, .card-toolbar, .filter-section,
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate,
        .dataTables_wrapper .dataTables_processing,
        .fv-row, .form-control, .form-label {
            display: none !important;
        }

        /* ========================================
           SOPHISTICATED TABLE DESIGN
           Modern corporate styling with elegant color scheme
           ======================================== */
        .table {
            border-collapse: collapse !important;
            width: 100% !important;
            margin-top: 20px !important; /* Normal spacing since we're using HTML elements */
            font-size: 10px !important;
            background: #ffffff !important;
            border: 1px solid #bdc3c7 !important;
            border-radius: 6px !important;
            overflow: hidden !important;
            box-shadow: 0 3px 12px rgba(52, 73, 94, 0.1) !important;
        }

        /* Elegant Table Header */
        .table thead th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
            color: #ffffff !important;
            font-weight: 600 !important;
            text-align: center !important;
            padding: 14px 10px !important;
            border: none !important;
            border-bottom: 2px solid #2c3e50 !important;
            font-size: 11px !important;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
        }

        /* Professional Table Body */
        .table tbody td {
            padding: 12px 10px !important;
            border: none !important;
            border-bottom: 1px solid #ecf0f1 !important;
            text-align: center !important;
            vertical-align: middle !important;
            background: #ffffff !important;
            color: #2c3e50 !important;
            font-weight: 400 !important;
            font-size: 10px !important;
            transition: background-color 0.2s ease !important;
        }

        /* Elegant Row Alternation */
        .table tbody tr:nth-child(even) {
            background: #f8f9fa !important;
        }

        .table tbody tr:hover {
            background: #e8f4f8 !important;
        }

        /* Sophisticated Footer/Totals */
        .table tfoot td {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            font-weight: 700 !important;
            color: #ffffff !important;
            padding: 16px 10px !important;
            border: none !important;
            border-top: 3px solid #2c3e50 !important;
            text-align: center !important;
            font-size: 11px !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Distinguished Sequential Column */
        .table tbody td:first-child {
            background: linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 100%) !important;
            font-weight: 600 !important;
            color: #34495e !important;
            width: 60px !important;
            text-align: center !important;
            border-right: 2px solid #bdc3c7 !important;
            font-size: 10px !important;
        }

        /* Enhanced Totals Row - Match Table Cells */
        .table .totals-row td {
            background: linear-gradient(135deg,  0%, #34495e 100%) !important;
            color: #ffffff !important;
            font-weight: 700 !important;
            font-size: 11px !important;
            padding: 16px 10px !important;
            border: none !important;
            border-top: 3px solid #2c3e50 !important;
            text-align: center !important;
            vertical-align: middle !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
        }

        /* Specific styling for each totals row cell */
        .table .totals-row .total-sequence {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
            width: 60px !important;
            border-right: 2px solid #1a252f !important;
            text-align: center !important;
        }

        .table .totals-row .total-date,
        .table .totals-row .total-details,
        .table .totals-row .total-account {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
        }

        .table .totals-row .total-debtor,
        .table .totals-row .total-creditor {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
            font-weight: 700 !important;
        }

        /* ========================================
           SOPHISTICATED VISUAL ENHANCEMENTS
           ======================================== */

        /* Amount Columns Styling */
        .table tbody td.debtor-column,
        .table tbody td.creditor-column {
            font-weight: 600 !important;
            background: #f8fff9 !important;
        }

        /* Negative amounts styling */
        .table tbody td.negative-amount {
            color: #e74c3c !important;
            background: #fdf2f2 !important;
            border-left: 3px solid #e74c3c !important;
        }

        /* ========================================
           SOPHISTICATED PAGE LAYOUT & BREAKS
           ======================================== */

        /* Premium Header Protection */
        .print-company-logo,
        #printable-section::before {
            page-break-inside: avoid;
            page-break-after: avoid;
            orphans: 3;
            widows: 3;
        }

        /* Table Structure Integrity */
        .table {
            page-break-inside: auto;
            margin-bottom: 25px;
        }

        .table thead {
            page-break-inside: avoid;
            page-break-after: avoid;
            display: table-header-group;
        }

        .table tbody tr {
            page-break-inside: avoid;
            page-break-after: auto;
            orphans: 2;
            widows: 2;
        }

        /* Keep standard table footer behavior */
        .table tfoot {
            display: table-footer-group;
            page-break-inside: avoid;
            page-break-before: avoid;
        }

        .totals-row {
            page-break-inside: avoid;
            page-break-before: avoid;
            display: table-row;
        }

        /* Use CSS to prevent footer repetition without changing table structure */
        @media print {
            /* Prevent automatic footer repetition on each page */
            .table tfoot {
                display: table-footer-group;
                break-inside: avoid;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .print-only-totals {
                display: table-footer-group !important;
                visibility: visible !important;
            }
        }

        /* ========================================
           PROFESSIONAL SPACING & TYPOGRAPHY
           ======================================== */

        /* Enhanced Readability */
        .table tbody td {
            line-height: 1.5 !important;
        }

        /* Professional Text Alignment */
        .text-start {
            text-align: center !important;
        }

        /* Hide Non-Essential Elements */
        .btn, .card-toolbar, .filter-section,
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate,
        .dataTables_wrapper .dataTables_processing,
        .fv-row, .form-control, .form-label {
            display: none !important;
        }

        /* ========================================
           ENHANCED TOTALS DISPLAY
           ======================================== */
        .totals-row {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            border-top: 3px solid #2c3e50 !important;
            font-weight: 700 !important;
        }

        .totals-row td {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #ffffff !important;
            font-weight: 700 !important;
            font-size: 11px !important;
            padding: 16px 10px !important;
            border: none !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Ensure totals row is properly styled */
        .totals-row td {
            border: 1px solid #dee2e6 !important;
            padding: 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
            font-size: 13px !important;
            font-weight: 600 !important;
            background-color: #f8f9fa !important;
        }

        /* Show totals only in print */
        .print-only-totals {
            display: table-footer-group !important;
            visibility: visible !important;
        }

        .print-only-totals tr {
            display: table-row !important;
            visibility: visible !important;
        }

        .print-only-totals td {
            display: table-cell !important;
            visibility: visible !important;
            border: 1px solid #dee2e6 !important;
            padding: 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
            font-size: 13px !important;
            font-weight: 700 !important;
        }

        /* ========================================
           MOBILE DEVICE PRINT OPTIMIZATION
           Comprehensive mobile print support for all screen sizes
           ======================================== */

        /* Mobile Print Layout - Force All Columns Visible */
        @media print and (max-width: 768px) {
            /* Override DataTables responsive hiding on mobile */
            .table .dtr-hidden,
            .table .d-none,
            .table .d-sm-none,
            .table .d-md-none {
                display: table-cell !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* Force table to use full width on mobile */
            .table {
                width: 100% !important;
                table-layout: fixed !important;
                border-collapse: collapse !important;
                font-size: 8px !important;
            }

            /* Mobile column width optimization */
            .table th,
            .table td {
                padding: 3px 2px !important;
                font-size: 7px !important;
                line-height: 1.2 !important;
                word-wrap: break-word !important;
                overflow: visible !important;
                text-overflow: clip !important;
                white-space: nowrap !important;
                max-width: none !important;
                min-width: 0 !important;
            }

            /* Sequential column - always visible and compact */
            .table th:first-child,
            .table td:first-child {
                width: 25px !important;
                max-width: 25px !important;
                min-width: 25px !important;
                padding: 2px 1px !important;
                font-size: 7px !important;
                text-align: center !important;
                display: table-cell !important;
                visibility: visible !important;
            }

            /* Distribute remaining width among data columns */
            .table th:not(:first-child),
            .table td:not(:first-child) {
                width: auto !important;
                max-width: none !important;
            }

            /* Header styling for mobile */
            .table thead th {
                font-size: 7px !important;
                padding: 3px 2px !important;
                text-transform: none !important;
                letter-spacing: 0 !important;
            }

            /* Totals row mobile optimization */
            .table .totals-row td {
                font-size: 7px !important;
                padding: 3px 2px !important;
            }
        }

        /* Extra Small Mobile Devices (320px-480px) */
        @media print and (max-width: 480px) {
            .table {
                font-size: 6px !important;
            }

            .table th,
            .table td {
                padding: 2px 1px !important;
                font-size: 6px !important;
                line-height: 1.1 !important;
            }

            .table th:first-child,
            .table td:first-child {
                width: 20px !important;
                max-width: 20px !important;
                min-width: 20px !important;
                font-size: 6px !important;
            }

            .table thead th {
                font-size: 6px !important;
                padding: 2px 1px !important;
            }

            .table .totals-row td {
                font-size: 6px !important;
                padding: 2px 1px !important;
            }
        }

        /* Tablet Print Layout (768px-1024px) */
        @media print and (min-width: 769px) and (max-width: 1024px) {
            .table {
                font-size: 9px !important;
            }

            .table th,
            .table td {
                padding: 4px 3px !important;
                font-size: 9px !important;
            }

            .table th:first-child,
            .table td:first-child {
                width: 35px !important;
                max-width: 35px !important;
            }
        }

        /* Force visibility overrides for all mobile print scenarios */
        @media print and (max-width: 768px) {
            /* Override any responsive classes that might hide columns */
            .dtr-hidden,
            .d-none,
            .d-sm-none,
            .d-md-none,
            .d-lg-none,
            .d-xl-none,
            .d-xxl-none,
            .hidden,
            .sr-only {
                display: table-cell !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: static !important;
                width: auto !important;
                height: auto !important;
                margin: 0 !important;
                padding: 2px 1px !important;
                border: 1px solid #ddd !important;
                clip: auto !important;
                overflow: visible !important;
            }

            /* Ensure table wrapper doesn't interfere */
            .dataTables_wrapper,
            .table-responsive {
                overflow: visible !important;
                width: 100% !important;
                height: auto !important;
                min-height: auto !important;
            }

            /* Force all table elements to be visible */
            table * {
                display: table-cell !important;
                visibility: visible !important;
            }

            table thead,
            table tbody,
            table tfoot {
                display: table-header-group !important;
                visibility: visible !important;
            }

            table tbody {
                display: table-row-group !important;
            }

            table tfoot {
                display: table-footer-group !important;
            }

            table tr {
                display: table-row !important;
                visibility: visible !important;
            }
        }

        /* ========================================
           FINAL POLISH & REFINEMENTS
           ======================================== */

        /* Ensure Color Accuracy */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

    }

    /* ========================================
       SCREEN VIEW - HIDE PRINT ELEMENTS
       ======================================== */
    @media screen {
        .company-header-box,
        .company-info-section,
        .report-title-box,
        .print-only-totals {
            display: none !important; /* Hidden in normal view */
        }
    }

    /* ========================================
       MOBILE SCREEN VIEW OPTIMIZATIONS
       Fix data display issues on mobile devices
       ======================================== */
    @media screen and (max-width: 768px) {
        /* Ensure DataTables wrapper doesn't hide content */
        .dataTables_wrapper {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch !important;
            width: 100% !important;
        }

        /* Ensure table is visible and scrollable on mobile */
        .table {
            min-width: 600px !important; /* Minimum width to show all columns */
            width: 100% !important;
            display: table !important;
            visibility: visible !important;
        }

        /* Ensure all table elements are visible */
        .table thead,
        .table tbody,
        .table tfoot {
            display: table-header-group !important;
            visibility: visible !important;
        }

        .table tbody {
            display: table-row-group !important;
        }

        .table tr {
            display: table-row !important;
            visibility: visible !important;
        }

        .table th,
        .table td {
            display: table-cell !important;
            visibility: visible !important;
            white-space: nowrap !important;
            padding: 8px 4px !important;
            font-size: 12px !important;
        }

        /* Sequence column mobile optimization */
        .table th:first-child,
        .table td:first-child {
            position: sticky !important;
            left: 0 !important;
            background: #f8f9fa !important;
            z-index: 10 !important;
            min-width: 40px !important;
            width: 40px !important;
        }

        /* Ensure DataTables controls are visible */
        .dataTables_length,
        .dataTables_filter,
        .dataTables_info,
        .dataTables_paginate {
            display: block !important;
            visibility: visible !important;
        }

        /* Fix responsive details button */
        .dtr-control {
            display: table-cell !important;
            visibility: visible !important;
        }

        /* Ensure card containers don't interfere */
        .card,
        .card-body {
            overflow: visible !important;
            width: 100% !important;
        }

        /* Fix filter section on mobile */
        .fv-row,
        .row {
            display: flex !important;
            flex-wrap: wrap !important;
            visibility: visible !important;
        }

        /* Ensure form controls are visible */
        .form-control,
        .form-label,
        .btn {
            display: block !important;
            visibility: visible !important;
        }
    }

    /* Extra small mobile devices */
    @media screen and (max-width: 480px) {
        .table {
            min-width: 500px !important;
            font-size: 11px !important;
        }

        .table th,
        .table td {
            padding: 6px 3px !important;
            font-size: 11px !important;
        }

        .table th:first-child,
        .table td:first-child {
            min-width: 35px !important;
            width: 35px !important;
        }
    }

    /* ========================================
       SCREEN VIEW - HIDE TOTALS ON SCREEN
       ======================================== */

    .print-only {
        display: none;
    }
    @media print {
        .print-only {
            display: table-row !important;
        }
        /* Style the totals row like the table header for print */
        .totals-row.print-only td {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: #fff !important;
            font-weight: bold !important;
            font-size: 12px !important;
            border: 1px solid #2c3e50 !important;
            text-align: center !important;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }

</style>
