@extends('company.layout.CompanyLayout')
@section('title')
    تقرير الميزانية
@endsection
@section('css')
    @include('company.reports.partials.unified-report-template')
    <style>
        /* Balances Report specific print styles */
        @media print {
            /* Hide the empty first column in balances report tables */
            .table th:first-child,
            .table td:first-child {
                display: none !important;
            }
            
            /* Ensure proper column alignment after hiding first column */
            .table th,
            .table td {
                text-align: center !important;
            }
            
            /* Hide the green totals rows in print */
            .table tbody tr:last-child {
                display: none !important;
            }
        }
    </style>
@endsection

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">

                    </div>
                    <!--end::Card title-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <div class="fv-row row mb-15">
                        <div class="row" id="filter-form">
                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">من تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="from_date" id="from_date" class="form-control mb-2" value=""/>
                            </div>

                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">الى تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="to_date" id="to_date" class="form-control mb-2" value=""/>
                            </div>

                            <!--begin::Action buttons-->
                            <div class="col-md-3">
                                <div class="d-flex justify-content-start">
                                    <!--begin::Clear Filters Button-->
                                    <button type="button" id="clear-filters" class="btn btn-light">
                                        <i class="ki-duotone ki-cross fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        مسح الفلاتر
                                    </button>
                                    <!--end::Clear Filters Button-->
                                </div>
                            </div>
                            <!--end::Action buttons-->

                        </div>

                    </div>
                    <!--begin::Table-->
                    @can('balances.print')
                    <button onclick="printSection()" class="btn btn-success">طباعة</button>
                    @endcan

                    <div id="printable-section" data-report-title="تقرير الميزانية" data-report-date="{{ now()->format('Y-m-d H:i') }}">
                    
                        <!-- Company Information Header Box -->
                        <div class="company-header-box">
                            @if($companyLogo)
                                <img src="{{ $companyLogo }}" class="print-company-logo" alt="شعار الشركة" style="display: none;">
                            @endif

                            <!-- Company Information Section -->
                            <div class="company-info-section">
                                <div class="company-name">🏛️ {{ Auth::guard('company')->user()->name ?? 'اسم الشركة' }}</div>
                                @if(Auth::guard('company')->user()->location || Auth::guard('company')->user()->city)
                                    <div>📍 {{ Auth::guard('company')->user()->location ? Auth::guard('company')->user()->location . (Auth::guard('company')->user()->city ? '، ' . Auth::guard('company')->user()->city : '') : '' }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->phone)
                                    <div>📞 {{ Auth::guard('company')->user()->phone }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->email)
                                    <div>✉️ {{ Auth::guard('company')->user()->email }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->tax_number)
                                    <div>🏢 {{ Auth::guard('company')->user()->tax_number }}</div>
                                @endif
                            </div>

                            <!-- Report Title Section - Inside Company Box -->
                            <div class="report-title-box">
                                <div class="report-title">تقرير الميزانية</div>
                                <div class="report-date">تاريخ التقرير: {{ now()->format('Y-m-d H:i:s') }}</div>
                            </div>
                        </div>

                        <!-- Header Divider Line -->
                        <div class="header-divider"></div>

                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="static-table-1">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الاصول</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">اسم الاصل</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الرصيد</th>

                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">

                        @foreach($basics as $basic)
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold">{{$basic['parent_name']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">{{$basic['name']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="basic-balance-{{$basic['id']}}">{{$basic['net_balance']}}</td>
                            </tr>

                        @endforeach

                        <tr class="odd">
                            <td class="dtr-control">
                                <div class="form-check form-check-sm form-check-custom form-check-solid">

                                </div>
                            </td>

                            <td class="text-gray-800 fs-5 fw-bold">الاصول</td>
                            <td class="text-gray-800 fs-5 fw-bold">العملاء</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-net-client">{{$net_client}}</td>
                        </tr>


                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المجموع</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;"></th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-sum-basics">{{abs($sum_basics)}}</th>


                        </tr>


                        </tbody>
                    </table>
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="static-table-2">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">حقوق الملكية</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">اسم الالتزام</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الرصيد</th>

                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">

                        @foreach($copyrights as $copyright)
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold">{{$copyright['parent_name']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">{{$copyright['name']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">{{$copyright['net_balance']}}</td>
                            </tr>

                        @endforeach


                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المجموع</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;"></th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-sum-copyright">{{abs($sum_copyright)}}</th>


                        </tr>


                        </tbody>
                    </table>
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="static-table-3">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الالتزامات</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">اسم الالتزام</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الرصيد</th>

                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">

                        @foreach($obligations as $obligation)
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold">{{$obligation['parent_name']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">{{$obligation['name']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">{{$obligation['net_balance']}}</td>
                            </tr>

                        @endforeach

                        <tr class="odd">
                            <td class="dtr-control">
                                <div class="form-check form-check-sm form-check-custom form-check-solid">

                                </div>
                            </td>

                            <td class="text-gray-800 fs-5 fw-bold">الالتزامات</td>
                            <td class="text-gray-800 fs-5 fw-bold">الموردين</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-net-supplier">{{$net_supplier}}</td>
                        </tr>

                        <tr class="odd">
                            <td class="dtr-control">
                                <div class="form-check form-check-sm form-check-custom form-check-solid">

                                </div>
                            </td>

                            <td class="text-gray-800 fs-5 fw-bold">الالتزامات</td>
                            <td class="text-gray-800 fs-5 fw-bold">ضريبة القيمة المضافة</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-net-tax">{{$net_tax}}</td>
                        </tr>



                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المجموع</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-sum-obligations">{{abs($sum_obligations)}}</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;"> ارباح/ خسائر "صافي الربح"</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-net">{{abs($net)}}</th>



                        </tr>


                        </tbody>
                    </table>
                    <!--end::Table-->
                    </div>
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Content container-->
    </div>




@endsection
@section('script')

<script>
    // Simple print function for static table
    function printSection() {
        // Show loading indicator
        const printBtn = document.querySelector('button[onclick="printSection()"]');
        if (printBtn) {
            const originalText = printBtn.innerHTML;
            printBtn.innerHTML = 'جاري الطباعة...';
            printBtn.disabled = true;

            // Trigger print after a short delay
            setTimeout(function() {
                window.print();

                // Reset button
                printBtn.innerHTML = originalText;
                printBtn.disabled = false;
            }, 100);
        } else {
            // Fallback if button not found
            window.print();
        }
    }

    // Real-time filtering functionality
    function updateTotals() {
        const fromDate = $('#from_date').val();
        const toDate = $('#to_date').val();

        console.log('Updating totals with dates:', fromDate, toDate);

        $.ajax({
            url: '{{ route('company.balances') }}',
            method: 'GET',
            data: {
                from_date: fromDate,
                to_date: toDate
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('Balances Response:', response);
                console.log('Basic Accounts:', response.basicAccounts);
                console.log('Debug Info:', response.debug);
                
                // Update ALL display values with proper formatting
                $('#display-net-client').text(response.netClient || '0.00');
                $('#display-net-supplier').text(response.netSupplier || '0.00');
                $('#display-net-tax').text(response.netTax || '0.00');
                $('#display-sum-basics').text(response.sumBasics || '0.00');
                $('#display-sum-copyright').text(response.sumCopyright || '0.00');
                $('#display-sum-obligations').text(response.sumObligations || '0.00');
                $('#display-net').text(response.net || '0.00');
                
                // Update individual basic accounts (assets)
                if (response.basicAccounts && response.basicAccounts.length > 0) {
                    console.log('Updating individual accounts...');
                    response.basicAccounts.forEach(function(account) {
                        console.log('Updating account ID:', account.id, 'Balance:', account.net_balance);
                        const element = $('#basic-balance-' + account.id);
                        if (element.length > 0) {
                            element.text(account.net_balance);
                            console.log('Updated element:', element.text());
                        } else {
                            console.log('Element not found for ID:', account.id);
                        }
                    });
                } else {
                    console.log('No basic accounts in response');
                }
                
                console.log('Totals and individual accounts updated successfully');
            },
            error: function(xhr, status, error) {
                console.error('Error updating totals:', error);
                console.error('Response:', xhr.responseText);
            }
        });
    }

    // Auto-refresh on date changes
    $('#from_date, #to_date').on('change', function() {
        console.log('Date changed:', $(this).attr('id'), $(this).val());
        updateTotals();
    });

    // Clear all filters
    $('#clear-filters').on('click', function() {
        console.log('Clearing filters');
        $('#from_date').val('');
        $('#to_date').val('');
        updateTotals();
    });


</script>

@endsection


