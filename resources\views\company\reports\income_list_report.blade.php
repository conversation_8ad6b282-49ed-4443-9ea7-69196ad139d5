@extends('company.layout.CompanyLayout')
@section('title')
    تقرير قائمة الدخل
@endsection
@section('css')
    @include('company.reports.partials.unified-report-template')
    <style>
        /* Income List Report specific print styles */
        @media print {
            /* Hide non-essential elements */
            .btn, #filter-form, .row.mt-12 {
                display: none !important;
            }

            /* Ensure all content is visible */
            .tab-content,
            .tab-pane,
            .card-body,
            .fv-row,
            .form-control,
            .form-label {
                display: block !important;
                visibility: visible !important;
            }

            .fv-row {
                display: flex !important;
            }

            /* Remove white backgrounds for income list report */
            .card,
            .card-body,
            .card-flush,
            .tab-content,
            .tab-pane,
            .fv-row,
            .col-md-2,
            .col-md-9,
            #income-data-container,
            #printable-section {
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Form styling for income list - transparent background */
            .form-control {
                border: 1px solid #dee2e6 !important;
                background: transparent !important;
                background-color: transparent !important;
                font-weight: bold !important;
                color: #2c3e50 !important;
                text-align: center !important;
                font-size: 14px !important;
            }

            .form-label {
                font-weight: 600 !important;
                color: #2c3e50 !important;
                font-size: 14px !important;
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Data rows styling */
            .fv-row {
                margin-bottom: 15px !important;
                border-bottom: 1px solid #dee2e6 !important;
                padding-bottom: 10px !important;
                align-items: center !important;
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Column layout */
            .col-md-2 {
                font-weight: 600 !important;
                color: #2c3e50 !important;
                flex: 0 0 30% !important;
                max-width: 30% !important;
                background: transparent !important;
                background-color: transparent !important;
            }

            .col-md-9 {
                text-align: left !important;
                flex: 0 0 70% !important;
                max-width: 70% !important;
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Card styling - remove all backgrounds */
            .card {
                box-shadow: none !important;
                border: none !important;
                margin-bottom: 0 !important;
                background: transparent !important;
                background-color: transparent !important;
            }

            .card-body {
                padding: 0 !important;
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Override any table backgrounds from unified template */
            .table {
                background: transparent !important;
                background-color: transparent !important;
            }

            .table tbody td {
                background: transparent !important;
                background-color: transparent !important;
            }

            .table tbody tr:nth-child(even) {
                background: transparent !important;
                background-color: transparent !important;
            }

            .table tbody tr:hover {
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Show company logo in print */
            .print-company-logo {
                display: block !important;
            }

            /* Ensure body and html have transparent background */
            body, html {
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Override any other background styles */
            * {
                background: transparent !important;
                background-color: transparent !important;
            }

            /* Exception: Keep form control borders for readability */
            .form-control {
                border: 1px solid #dee2e6 !important;
            }
        }
    </style>
@endsection

@section('content')
    <div id="kt_app_content_container" class="app-container container-xxl">

        <!--begin::Card-->

        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header pt-8">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>قائمة الدخل</h2>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body">
            <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">

                <div class="fv-row row mb-15">
                    <div class="row" id="filter-form">
                        <div class="col-md-1 d-flex align-items-center">
                            <label class="fs-2 form-label">من تاريخ</label>
                        </div>
                        <div class="col-md-3">
                            <input type="date" name="from_date" id="from_date" class="form-control mb-2" value=""/>
                        </div>

                        <div class="col-md-1 d-flex align-items-center">
                            <label class="fs-2 form-label">الى تاريخ</label>
                        </div>
                        <div class="col-md-3">
                            <input type="date" name="to_date" id="to_date" class="form-control mb-2" value=""/>
                        </div>

                        <!--begin::Action buttons-->
                        <div class="col-md-3">
                            <div class="d-flex justify-content-start">
                                <!--begin::Clear Filters Button-->
                                <button type="button" id="clear-filters" class="btn btn-light">
                                    <i class="ki-duotone ki-cross fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                    مسح الفلاتر
                                </button>
                                <!--end::Clear Filters Button-->
                            </div>
                        </div>
                        <!--end::Action buttons-->
                    </div>
                </div>
                <div id="printable-section">

                    <!-- Company Information Header Box -->
                    <div class="company-header-box">
                        <img src="{{ $companyLogo }}" class="print-company-logo" alt="شعار الشركة" style="display: none;">

                        <!-- Company Information Section -->
                        <div class="company-info-section">
                            <div class="company-name">🏛️ {{ Auth::guard('company')->user()->name ?? 'اسم الشركة' }}</div>
                            @if(Auth::guard('company')->user()->location || Auth::guard('company')->user()->city)
                                <div>📍 {{ Auth::guard('company')->user()->location ? Auth::guard('company')->user()->location . (Auth::guard('company')->user()->city ? '، ' . Auth::guard('company')->user()->city : '') : '' }}</div>
                            @endif
                            @if(Auth::guard('company')->user()->phone)
                                <div>📞 {{ Auth::guard('company')->user()->phone }}</div>
                            @endif
                            @if(Auth::guard('company')->user()->email)
                                <div>✉️ {{ Auth::guard('company')->user()->email }}</div>
                            @endif
                            @if(Auth::guard('company')->user()->tax_number)
                                <div>🏢 {{ Auth::guard('company')->user()->tax_number }}</div>
                            @endif
                        </div>

                        <!-- Report Title Section - Inside Company Box -->
                        <div class="report-title-box">
                            <div class="report-title">تقرير قائمة الدخل</div>
                            <div class="report-date">تاريخ التقرير: {{ now()->format('Y-m-d H:i:s') }}</div>
                        </div>
                    </div>


                <div class="tab-content">
                    <div class="tab-pane fade show active" role="tab-panel">
                        <div class="d-flex flex-column gap-7 gap-lg-10">
                            <div class="card card-flush py-4">
                                <div class="card-body pt-0" id="income-data-container">
                                    @include('company.reports.partials.income-data')
                                </div>
                               </div>

                        </div>

                    </div>



                </div>
                </div>
                <!--begin::Action buttons-->
                <div class="row mt-12">
                    <div class="col-md-9 offset-md-3">
                        <!--begin::Cancel-->
                        <a href="{{route('company.home')}}" class="btn btn-light me-5">رجوع</a>
                        <!--end::Cancel-->
                        <!--end::Button-->
                        @can('income_list_report.print')
                        <button onclick="printSection()" class="btn btn-success">طباعة</button>
                        @endcan

                    </div>
                </div>
                <!--begin::Action buttons-->

            </div>
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>

@endsection

@section('script')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            // Function to load income data via AJAX
            function loadIncomeData() {
                var fromDate = $('#from_date').val();
                var toDate = $('#to_date').val();

                $.ajax({
                    url: '{{ route('company.income_list_report') }}',
                    type: 'GET',
                    data: {
                        from_date: fromDate,
                        to_date: toDate,
                        ajax: true
                    },
                    success: function(response) {
                        // Update the income data container with new data
                        $('#income-data-container').html(response.html);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading income data:', error);
                    }
                });
            }

            // Auto-refresh on filter changes
            $('#from_date, #to_date').on('change', function() {
                loadIncomeData();
            });

            // Clear filters functionality
            $('#clear-filters').on('click', function() {
                $('#from_date').val('');
                $('#to_date').val('');
                loadIncomeData();
            });

            // Print function
            window.printSection = function() {
                window.print();
            };
        });
    </script>
@endsection
