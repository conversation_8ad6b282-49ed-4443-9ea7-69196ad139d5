@extends('company.layout.CompanyLayout')
@section('title')
    تقرير المشتريات
@endsection
@section('css')
    @include('company.reports.partials.unified-report-template')
@endsection

@section('content')
    @php
        $filters = [
            ['name' => 'from_date', 'label' => 'من تاريخ', 'type' => 'date'],
            ['name' => 'to_date', 'label' => 'الى تاريخ', 'type' => 'date']
        ];

        $summaryCards = [
            ['id' => 'display-opening-balance', 'value' => $balance, 'label' => 'الرصيد الافتتاحي'],
            ['id' => 'display-total-debtor', 'value' => $debtor, 'label' => 'مدين'],
            ['id' => 'display-total-creditor', 'value' => $creditor, 'label' => 'دائن'],
            ['id' => 'display-total-balance', 'value' => $debtor-$creditor, 'label' => 'الاجمالي']
        ];

        $tableHeaders = ['التاريخ', 'التفاصيل', 'المورد', 'مدين', 'دائن', 'الحساب'];
        $reportTitle = 'تقرير المشتريات';
        $printPermission = 'purchases_report.print';
        $showTotals = true;
        $totalColspan = 4;
        $totalColumns = [
            ['id' => 'print-total-debtor'],
            ['id' => 'print-total-creditor']
        ];
    @endphp

    @include('company.reports.partials.unified-report-structure')
@endsection
@section('script')
    @php
        $ajaxRoute = route('company.purchases_report');
        $filterParams = ['from_date', 'to_date'];
        $reportColumns = [
            ['data' => 'date', 'name' => 'date'],
            ['data' => 'details', 'name' => 'details'],
            ['data' => 'supplier_name', 'name' => 'supplier_name'],
            ['data' => 'debtor', 'name' => 'debtor'],
            ['data' => 'creditor', 'name' => 'creditor'],
            ['data' => 'account_name', 'name' => 'account_name']
        ];
    @endphp

    @include('company.reports.partials.unified-report-script')
@endsection


