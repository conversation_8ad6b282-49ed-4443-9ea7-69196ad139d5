<!DOCTYPE html>
<html lang="ar" dir="rtl"  >
<!--begin::Head-->
<head><base href=""/>
    <title>@yield('title')</title>
    <meta charset="utf-8" />
    <meta name="description" content="The most advanced Bootstrap 5 Admin Theme with 40 unique prebuilt layouts on Themeforest trusted by 100,000 beginners and professionals. Multi-demo, Dark Mode, RTL support and complete React, Angular, Vue, Asp.Net Core, Rails, Spring, Blazor, Django, Express.js, Node.js, Flask, Symfony & Laravel versions. Grab your copy now and get life-time updates for free." />
    <meta name="keywords" content="metronic, bootstrap, bootstrap 5, angular, VueJs, React, Asp.Net Core, Rails, Spring, Blazor, Django, Express.js, Node.js, Flask, Symfony & Laravel starter kits, admin themes, web design, figma, web development, free templates, free admin themes, bootstrap theme, bootstrap template, bootstrap dashboard, bootstrap dak mode, bootstrap button, bootstrap datepicker, bootstrap timepicker, fullcalendar, datatables, flaticon" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="Metronic - Bootstrap Admin Template, HTML, VueJS, React, Angular. Laravel, Asp.Net Core, Ruby on Rails, Spring Boot, Blazor, Django, Express.js, Node.js, Flask Admin Dashboard Theme & Template" />
    <meta property="og:url" content="https://keenthemes.com/metronic" />
    <meta property="og:site_name" content="Keenthemes | Metronic" />
    <link rel="canonical" href="https://preview.keenthemes.com/metronic8" />
    <link rel="shortcut icon" href="{{asset('/manager_assest/dist/assets/media/logos/favicon.ico')}}" />
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <!--end::Fonts-->
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link href="{{asset('/manager_assest/dist/assets/plugins/global/plugins.bundle.rtl.css')}}" rel="stylesheet" type="text/css" />
    <link href="{{asset('/manager_assest/dist/assets/css/style.bundle.rtl.css')}}" rel="stylesheet" type="text/css" />
    <link href="{{asset('/manager_assest/dist/assets/plugins/custom/toastr/build/toastr.css')}}" rel="stylesheet" type="text/css" />
    <!--end::Global Stylesheets Bundle-->
    @yield('css')

    <style>
        @media print {
            body * {
                visibility: hidden;
            }

            #printable-section, #printable-section * {
                visibility: visible;
            }
            #printable-section {
                position: absolute;

                top: 0;
                left: 0;
                right: 0;
                left: 0;
            }
        }
        body, #kt_app_content_container {
            background: #f7f9fa !important;
        }
        .dashboard-summary-row {
            display: grid !important;
            grid-template-columns: repeat(5, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }
        @media (max-width: 1200px) {
            .dashboard-summary-row {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        @media (max-width: 900px) {
            .dashboard-summary-row {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 600px) {
            .dashboard-summary-row {
                grid-template-columns: 1fr;
            }
        }
        .dashboard-summary-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            padding: 20px 0 12px 0;
            min-height: 100px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .main-number {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 700;
            color: #222;
            font-family: 'Tajawal', 'Cairo', 'Segoe UI', Arial, sans-serif;
            gap: 0.2em;
        }
        .number-text {
            line-height: 1;
            font-family: inherit;
            font-weight: inherit;
            font-size: inherit;
        }
        .currency .riyal-svg {
            height: 1.7rem;
            width: auto;
            display: inline-block;
            vertical-align: middle;
            color: inherit;
        }
        .dashboard-summary-card .card-label {
            color: #888;
            font-size: 1.1rem;
            font-weight: 500;
        }
        .dashboard-charts-row .card {
            border-radius: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        }
        
        /* Logo container constraints */
        .app-sidebar-logo {
            overflow: visible;
            max-width: 100%;
            padding: 15px 10px;
        }
        
        .app-sidebar-logo img {
            max-width: 180px;
            max-height: 60px;
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: 8px;
            display: block;
            margin: 0 auto;
        }
        
        .app-sidebar-logo a {
            display: block;
            width: 100%;
            text-align: center;
            padding: 5px;
        }
        
        /* Ensure logo is never cut off */
        .app-sidebar-logo {
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Minimized sidebar logo */
        .app-sidebar-minimize .app-sidebar-logo img {
            max-height: 35px;
            max-width: 35px;
        }
    </style>

</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true" data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true" data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true" data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" class="app-default" class="print-content-only app-default">
<!--begin::Theme mode setup on page load-->
<script>var defaultThemeMode = "dark"; var themeMode; if ( document.documentElement ) { if ( document.documentElement.hasAttribute("data-bs-theme-mode")) { themeMode = document.documentElement.getAttribute("data-bs-theme-mode"); } else { if ( localStorage.getItem("data-bs-theme") !== null ) { themeMode = localStorage.getItem("data-bs-theme"); } else { themeMode = defaultThemeMode; } } if (themeMode === "system") { themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"; } document.documentElement.setAttribute("data-bs-theme", themeMode); }</script>
<!--end::Theme mode setup on page load-->
<!--begin::App-->
<div class="d-flex flex-column flex-root app-root" id="kt_app_root">
    <!--begin::Page-->
    <div class="app-page flex-column flex-column-fluid" id="kt_app_page">
        <!--begin::Header-->
        <div id="kt_app_header" class="app-header">
            <!--begin::Header container-->
            <div class="app-container container-fluid d-flex align-items-stretch justify-content-between" id="kt_app_header_container">
                <!--begin::Sidebar mobile toggle-->
                <div class="d-flex align-items-center d-lg-none ms-n3 me-1 me-md-2" title="Show sidebar menu">
                    <div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_sidebar_mobile_toggle">
                        <i class="ki-duotone ki-abstract-14 fs-2 fs-md-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                    </div>
                </div>
                <!--end::Sidebar mobile toggle-->
                <!--begin::Mobile logo-->
                <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
                    <a href="../../demo1/dist/index.html" class="d-lg-none">
                        <img alt="Logo" src="{{asset('/manager_assest/dist/assets/media/logos/default-small.svg')}}" class="h-30px" />
                    </a>
                </div>
                <!--end::Mobile logo-->
                <!--begin::Header wrapper-->
                <div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1" id="kt_app_header_wrapper">
                    <!--begin::Menu wrapper-->
                    <div class="app-header-menu app-header-mobile-drawer align-items-stretch" data-kt-drawer="true" data-kt-drawer-name="app-header-menu" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="250px" data-kt-drawer-direction="end" data-kt-drawer-toggle="#kt_app_header_menu_toggle" data-kt-swapper="true" data-kt-swapper-mode="{default: 'append', lg: 'prepend'}" data-kt-swapper-parent="{default: '#kt_app_body', lg: '#kt_app_header_wrapper'}">

                    </div>
                    <!--end::Menu wrapper-->
                    <!--begin::Navbar-->
                    <div class="app-navbar flex-shrink-0">
                        <!--begin::Notifications-->
                        <div class="app-navbar-item ms-1 ms-md-3">
                            <!--begin::Menu- wrapper-->
                            <div class="position-relative btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-30px h-30px w-md-40px h-md-40px" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
                                <i class="fas fa-bell fs-2"></i>
                                <span class="notification-dot" id="notification-dot" style="display: none;"></span>
                            </div>

                            <!--begin::Menu-->
                            <div class="menu menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px" data-kt-menu="true" id="kt_menu_notifications">
                                <!--begin::Heading-->
                                <div class="d-flex flex-column bgi-no-repeat rounded-top">
                                    <!--begin::Title-->
                                    <h3 class="fw-semibold px-9 mt-10 mb-6 text-bold">الإشعارات</h3>
                                    <!--end::Title-->
                                    <ul class="nav nav-line-tabs nav-line-tabs-2x nav-stretch fw-semibold px-9" role="tablist"></ul>
                                </div>
                                <!--end::Heading-->
                                <!--begin::Tab content-->
                                <div class="tab-content">
                                    <!--begin::Tab panel-->
                                    <div class="tab-pane fade active show" role="tabpanel">
                                        <div id="notifications-container">

                                        </div>
                                    </div>
                                </div>
                                <!--end::Tab content-->
                            </div>
                            <!--end::Menu wrapper-->
                        </div>
                        <!--end::Notifications-->
                        <div class="app-navbar-item ms-1 ms-md-3">
                            <!--begin::Menu toggle-->
                            <a href="#" class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-30px h-30px w-md-40px h-md-40px" data-kt-menu-trigger="{default:'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
                                <i class="ki-duotone ki-night-day theme-light-show fs-2 fs-lg-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                    <span class="path4"></span>
                                    <span class="path5"></span>
                                    <span class="path6"></span>
                                    <span class="path7"></span>
                                    <span class="path8"></span>
                                    <span class="path9"></span>
                                    <span class="path10"></span>
                                </i>
                                <i class="ki-duotone ki-moon theme-dark-show fs-2 fs-lg-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </a>
                            <!--begin::Menu toggle-->
                            <!--begin::Menu-->
                            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-title-gray-700 menu-icon-gray-500 menu-active-bg menu-state-color fw-semibold py-4 fs-base w-150px" data-kt-menu="true" data-kt-element="theme-mode-menu">
                                <!--begin::Menu item-->
                                <div class="menu-item px-3 my-0">
                                    <a href="#" class="menu-link px-3 py-2" data-kt-element="mode" data-kt-value="light">
												<span class="menu-icon" data-kt-element="icon">
													<i class="ki-duotone ki-night-day fs-2">
														<span class="path1"></span>
														<span class="path2"></span>
														<span class="path3"></span>
														<span class="path4"></span>
														<span class="path5"></span>
														<span class="path6"></span>
														<span class="path7"></span>
														<span class="path8"></span>
														<span class="path9"></span>
														<span class="path10"></span>
													</i>
												</span>
                                        <span class="menu-title">فاتح</span>
                                    </a>
                                </div>
                                <!--end::Menu item-->
                                <!--begin::Menu item-->
                                <div class="menu-item px-3 my-0">
                                    <a href="#" class="menu-link px-3 py-2" data-kt-element="mode" data-kt-value="dark">
												<span class="menu-icon" data-kt-element="icon">
													<i class="ki-duotone ki-moon fs-2">
														<span class="path1"></span>
														<span class="path2"></span>
													</i>
												</span>
                                        <span class="menu-title">ليلي</span>
                                    </a>
                                </div>
                                <!--end::Menu item-->

                            </div>
                            <!--end::Menu-->
                        </div>
                        <!--end::Theme mode-->
                        <!--begin::User menu-->
                        <div class="app-navbar-item ms-1 ms-md-3" id="kt_header_user_menu_toggle">
                            <!--begin::Menu wrapper-->
                            <div class="cursor-pointer symbol symbol-30px symbol-md-40px" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
                                <img src="{{asset('/manager_assest/dist/assets/media/avatars/300-1.jpg')}}" alt="user" />
                            </div>
                            <!--begin::User account menu-->
                            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px" data-kt-menu="true">
                                <!--begin::Menu item-->
                                <div class="menu-item px-3">
                                    <div class="menu-content d-flex align-items-center px-3">
                                        <!--begin::Avatar-->
                                        <div class="symbol symbol-50px me-5">
                                            <img alt="Logo" src="{{asset('/manager_assest/dist/assets/media/avatars/300-1.jpg')}}" />
                                        </div>
                                        <!--end::Avatar-->
                                        <!--begin::Username-->
                                        <div class="d-flex flex-column">
                                            @auth
                                            <div class="fw-bold d-flex align-items-center fs-5">{{ Auth::user()->name }}</div>
                                            <a href="#" class="fw-semibold text-muted text-hover-primary fs-7">{{ Auth::user()->email }}</a>
                                            @endauth
                                        </div>
                                        <!--end::Username-->
                                    </div>
                                </div>
                                <!--end::Menu item-->
                                <!--begin::Menu separator-->
                                <div class="separator my-2"></div>
                                <!--end::Menu separator-->
                                <!--begin::Menu item-->
                                <div class="menu-item px-5">
                                    <a href="../../demo1/dist/account/overview.html" class="menu-link px-5">الملف الشخصي</a>
                                </div>
                                <!--end::Menu item-->

                                <!--begin::Menu item-->
                                <div class="menu-item px-5">
                                    <form id="logout-form" action="{{ url("/company/logout") }}" method="POST" style="display: none;">
                                        {{ csrf_field() }}
                                    </form>
                                    <a href="{{ url("/company/logout")}}" onclick="event.preventDefault();
                                                 document.getElementById('logout-form').submit();" class="menu-link px-5">تسجيل الخروج</a>
                                </div>
                                <!--end::Menu item-->
                            </div>
                            <!--end::User account menu-->
                            <!--end::Menu wrapper-->
                        </div>
                        <!--end::User menu-->
                        <!--begin::Header menu toggle-->
                        <div class="app-navbar-item d-lg-none ms-2 me-n2" title="Show header menu">
                            <div class="btn btn-flex btn-icon btn-active-color-primary w-30px h-30px" id="kt_app_header_menu_toggle">
                                <i class="ki-duotone ki-element-4 fs-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                            </div>
                        </div>
                        <!--end::Header menu toggle-->
                    </div>
                    <!--end::Navbar-->
                </div>
                <!--end::Header wrapper-->
            </div>
            <!--end::Header container-->
        </div>
        <!--end::Header-->
        <!--begin::Wrapper-->
        <div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
            <!--begin::Sidebar-->
            <div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
                <!--begin::Logo-->
                <div class="app-sidebar-logo px-6" id="kt_app_sidebar_logo">
                    <!--begin::Logo image-->
                    <a href="{{route('company.home')}}" class="d-block w-100">
                        @if($companyLogo)
                            <img alt="Logo" src="{{$companyLogo}}" class="app-sidebar-logo-default" />
                        @endif
                    </a>
                    <!--end::Logo image-->


                    <div id="kt_app_sidebar_toggle" class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary body-bg h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="app-sidebar-minimize">
                        <i class="ki-duotone ki-double-left fs-2 rotate-180">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                    </div>
                    <!--end::Sidebar toggle-->
                </div>
                <!--end::Logo-->
                <!--begin::sidebar menu-->
                <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
                    <!--begin::Menu wrapper-->
                    <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper hover-scroll-overlay-y my-5" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer" data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
                        <!--begin::Menu-->
                        <div class="menu menu-column menu-rounded menu-sub-indention px-3" id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">

                            <!--begin:Menu item-->
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                <!--begin:Menu link-->
                                <span class="menu-link">
											<span class="menu-icon">
												<i class="ki-duotone ki-chart-simple-2 fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
												</i>
											</span>
											<span class="menu-title">الأمور المحاسبية</span>
											<span class="menu-arrow"></span>
										</span>
                                <!--end:Menu link-->
                                <!--begin:Menu sub-->
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <!--begin:Menu item-->
                                    @can('sub_accounts.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.sub_accounts.index')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">دليل الحسابات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                     <!--begin:Menu item-->
                                    @can('transfer.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.transfer.index')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">تحويل أرصدة مالية</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('categories.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.categories.index')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">تصنيف المنتجات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('products.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.products.index')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">المنتجات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                </div>
                                <!--end:Menu sub-->
                            </div>
                            <!--end:Menu item-->

                            <!--begin:Menu item-->
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                <!--begin:Menu link-->
                                <span class="menu-link">
											<span class="menu-icon">
												<i class="ki-duotone ki-basket fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
												</i>
											</span>
											<span class="menu-title">المبيعات</span>
											<span class="menu-arrow"></span>
										</span>
                                <!--end:Menu link-->
                                <!--begin:Menu sub-->
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <!--begin:Menu item-->
                                    @can('clients.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.clients.index')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">العملاء</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('sales_invoice.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.sales.invoice')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">فاتورة المبيعات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('receipts.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.bills.receipts')}}" >
                                                    <span class="menu-bullet">
                                                        <span class="bullet bullet-dot">
                                                    </span>
                                                    </span>
                                            <span class="menu-title">سند قبض</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('return_sales.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.return_sales.index')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">مرتجع المبيعات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('offers_invoice.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.offers.invoice')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">فاتورة عرض سعر</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                </div>
                                <!--end:Menu sub-->
                            </div>
                            <!--end:Menu item-->

                            <!--begin:Menu item-->
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                <!--begin:Menu link-->
                                <span class="menu-link">
											<span class="menu-icon">
												<i class="ki-duotone ki-delivery fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
												</i>
											</span>
											<span class="menu-title">المشتريات</span>
											<span class="menu-arrow"></span>
										</span>
                                <!--end:Menu link-->
                                <!--begin:Menu sub-->
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <!--begin:Menu item-->
                                    @can('suppliers.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.suppliers.index')}}" >
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot">
                                            </span>
                                            </span>
                                            <span class="menu-title">الموردين</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('purchases_invoice.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.purchases.invoice')}}" >
                                                    <span class="menu-bullet">
                                                        <span class="bullet bullet-dot">
                                                    </span>
                                                    </span>
                                            <span class="menu-title">فاتورة المشتريات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('expenses.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.bills.expenses')}}" >
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot">
                                                </span>
                                                </span>
                                            <span class="menu-title">سند الصرف</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('return_purchases.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.return_purchases.index')}}" >
                                                    <span class="menu-bullet">
                                                        <span class="bullet bullet-dot">
                                                    </span>
                                                    </span>
                                            <span class="menu-title">مرتجع المشتريات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                </div>
                                <!--end:Menu sub-->
                            </div>
                            <!--end:Menu item-->

                            <!--begin:Menu item-->
                            @can('daly_expenses.index')
                            <div class="menu-item">
                                <!--begin:Menu link-->
                                <a class="menu-link" href="{{route('company.daly_expenses.index')}}" >
											<span class="menu-icon">
												<i class="ki-duotone ki-dollar fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
												</i>
											</span>
                                    <span class="menu-title">المصروفات</span>
                                </a>
                                <!--end:Menu link-->
                            </div>
                            @endcan
                            <!--end:Menu item-->

                            <!--begin:Menu item-->
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                <!--begin:Menu link-->
                                <span class="menu-link">
											<span class="menu-icon">
												<i class="ki-duotone ki-chart-pie-3 fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
												</i>
											</span>
											<span class="menu-title">التقارير</span>
											<span class="menu-arrow"></span>
										</span>
                                <!--end:Menu link-->
                                <!--begin:Menu sub-->
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <!--begin:Menu item-->
                                    @can('income_list_report.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.income_list_report')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">قائمة الدخل</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('account_statement.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.account_statement')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">كشف حساب</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('clients_report.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.clients_report')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">تقرير العملاء</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('suppliers_report.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.suppliers_report')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">تقرير الموردين</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('sales_report.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.sales_report')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">تقرير المبيعات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('purchases_report.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.purchases_report')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">تقرير المشتريات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('expenses_report.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.expenses_report')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">تقرير مصروفات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    @can('products_report.index')
                                    <!--begin:Menu item-->
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.products_report')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">تقرير المنتجات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    <!--end:Menu item-->
                                    @endcan
                                    @can('tax_report.index')
                                    <!--begin:Menu item-->
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.tax_report')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">تقرير الضريبة</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('trial_balance.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.trial_balance')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">ميزان المراجعة</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('balances.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.balances')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">الميزانية</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->


                                </div>
                                <!--end:Menu sub-->
                            </div>
                            <!--end:Menu item-->

                            <!--begin:Menu item-->
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                <!--begin:Menu link-->
                                <span class="menu-link">
											<span class="menu-icon">
												<i class="ki-duotone ki-people fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
												</i>
											</span>
											<span class="menu-title">شؤون الموظفين</span>
											<span class="menu-arrow"></span>
										</span>
                                <!--end:Menu link-->
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    @can('employees.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.employees.index')}}" >
                                                    <span class="menu-bullet">
                                                        <span class="bullet bullet-dot">
                                                    </span>
                                                    </span>
                                            <span class="menu-title">الموظفين</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan

                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.list_vacations.index')}}" >
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot">
                                                </span>
                                                </span>
                                            <span class="menu-title">الإجازات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                        <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.payrolls')}}" >
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot">
                                                </span>
                                                </span>
                                            <span class="menu-title">مسير الرواتب</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>

                                    @can('job_title.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.job_titles.index')}}" >
                                                    <span class="menu-bullet">
                                                        <span class="bullet bullet-dot">
                                                    </span>
                                                    </span>
                                            <span class="menu-title">المسميات الوظيفية</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan

                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.managements.index')}}" >
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot">
                                                </span>
                                                </span>
                                            <span class="menu-title">الإدارات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>

                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.departments.index')}}" >
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot">
                                                </span>
                                                </span>
                                            <span class="menu-title">الأقسام</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                </div>
                            </div>


                            <!--begin:Menu item-->
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                <!--begin:Menu link-->
                                <span class="menu-link">
											<span class="menu-icon">
												<i class="ki-duotone ki-document fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
												</i>
											</span>
											<span class="menu-title">النماذج الادارية</span>
											<span class="menu-arrow"></span>
										</span>
                                <!--end:Menu link-->
                                <!--begin:Menu sub-->
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <!--begin:Menu item-->
                                    @can('vacations.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.vacations.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">طلبات الاجازة</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('housing_allowances.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.housing_allowances.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">طلب بدل سكن</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('resignations.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.resignations.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">طلب استقالة</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('handovers.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.handovers.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">نموذج استلام وتسليم</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('claims.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.claims.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">نموذج تعويض تكاليف</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('salaries.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.salaries.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">نموذج راتب مقدم</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('travels.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.travels.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">نموذج رحلة عمل</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    @can('evaluations.index')
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.evaluations.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">نموذج تقييم الاداء الوظيفي</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    @endcan
                                    <!--end:Menu item-->

                                </div>
                                <!--end:Menu sub-->
                            </div>
                            <!--end:Menu item-->

                            <!--begin:Menu item-->
                            @can('tasks.index')
                            <div class="menu-item">
                                <!--begin:Menu link-->
                                <a class="menu-link" href="{{route('company.tasks.index')}}" >
											<span class="menu-icon">
												<i class="ki-duotone ki-check-square fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
												</i>
											</span>
                                    <span class="menu-title">المهام</span>
                                </a>
                                <!--end:Menu link-->
                            </div>
                            @endcan
                            <!--end:Menu item-->

                            <!--begin:Menu item-->
                            @can('subscription.index')
                            @if(Auth::user('company')->parent == 0)
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                <!--begin:Menu link-->
                                <span class="menu-link">
											<span class="menu-icon">
												<i class="ki-duotone ki-credit-cart fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
													<span class="path4"></span>
												</i>
											</span>
											<span class="menu-title">الاشتراكات</span>
											<span class="menu-arrow"></span>
										</span>
                                <!--end:Menu link-->
                                <!--begin:Menu sub-->
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <!--begin:Menu item-->
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.subscription.index')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">الباقات المتاحة</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.subscription.show')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">تفاصيل الاشتراك</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.subscription.history')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">سجل الاشتراكات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    <!--end:Menu item-->
                                    <!--begin:Menu item-->
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="{{route('company.subscription.payment_history')}}">
													<span class="menu-bullet">
														<span class="bullet bullet-dot"></span>
													</span>
                                            <span class="menu-title">سجل المدفوعات</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                    <!--end:Menu item-->
                                </div>
                                <!--end:Menu sub-->
                            </div>
                            @endif
                            @endcan

                            <!--begin:Menu item-->
                            @can('general_settings.index')
                            <div class="menu-item">
                                <!--begin:Menu link-->
                                <a class="menu-link" href="{{route('company.settings.general')}}" >
											<span class="menu-icon">
												<span class="menu-icon">
												<i class="ki-duotone ki-setting-2 fs-2">
													<span class="path1"></span>
													<span class="path2"></span>
													<span class="path3"></span>
													<span class="path4"></span>
												</i>
											</span>
											</span>
                                    <span class="menu-title">الإعدادات العامة</span>
                                </a>
                                <!--end:Menu link-->
                            </div>
                            @endcan


                        </div>
                        <!--end::Menu-->
                    </div>
                    <!--end::Menu wrapper-->
                </div>
                <!--end::sidebar menu-->

            </div>
            <!--end::Sidebar-->
            <!--begin::Main-->
            <div class="d-flex flex-column flex-column-fluid">
                <div class="app-main flex-column flex-row-fluid" id="kt_app_main"Calendar>
                    <!--begin::Content wrapper-->
                    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                        <!--begin::Toolbar container-->
                        <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
                            <!--begin::Page title-->
                            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                                <!--begin::Title-->
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">@yield('title')</h1>
                                <!--end::Title-->
                                <!--begin::Breadcrumb-->
                                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                                    <!--begin::Item-->
                                    <a class="breadcrumb-item text-muted">
                                        <a href="{{route('company.home')}}" class="text-muted text-hover-primary">الرئيسية</a>
                                    </a>
                                    <!--end::Item-->
                                    <!--begin::Item-->
                                    <li class="breadcrumb-item">
                                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                                    </li>
                                    <!--end::Item-->
                                    <!--begin::Item-->
                                    <li class="breadcrumb-item text-muted">@yield('title')</li>
                                    <!--end::Item-->
                                </ul>
                                <!--end::Breadcrumb-->
                            </div>
                            <!--end::Page title-->

                        </div>
                        <!--end::Toolbar container-->
                    </div>
                    @if($errors->any())
                        @foreach ($errors->all() as $error)
                            <div>{{ $error }}</div>
                        @endforeach
                    @endif
                    @yield('content')

                </div>
                <!--end::Content wrapper-->
                <!--begin::Footer-->
                <div id="kt_app_footer" class="app-footer">
                    <!--begin::Footer container-->
                    <div class="app-container container-fluid d-flex flex-column flex-md-row flex-center flex-md-stack py-3">
                        <!--begin::Copyright-->
                        <div class="text-dark order-2 order-md-1">
                            <span class="text-muted fw-semibold me-1">2023&copy;</span>
                            <a href="https://keenthemes.com" target="_blank" class="text-gray-800 text-hover-primary">osama hassan</a>
                        </div>
                        <!--end::Copyright-->

                        <!--end::Menu-->
                    </div>
                    <!--end::Footer container-->
                </div>
                <!--end::Footer-->
            </div>
            <!--end:::Main-->
        </div>
        <!--end::Wrapper-->
    </div>
    <!--end::Page-->
</div>

<script>var hostUrl = "assets/";</script>
<script src="{{asset('/manager_assest/dist/assets/plugins/global/plugins.bundle.js')}}"></script>
<script src="{{asset('/manager_assest/dist/assets/js/scripts.bundle.js')}}"></script>
<script src="{{asset('/manager_assest/dist/assets/js/manager.js')}}"></script>
<script src="{{asset('/manager_assest/dist/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js')}}"></script>
<script src="{{asset('/manager_assest/dist/assets/plugins/custom/datatables/datatables.bundle.js')}}"></script>

<script src="{{ asset("/manager_assest/dist/assets/plugins/custom/toastr/build/toastr.min.js") }}" type="text/javascript"></script>


<script>
    toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": false,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "preventDuplicates": false,
        "onclick": null,
        "showDuration": "100",
        "hideDuration": "2000",
        "timeOut": "10000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
    };
    @if(Session::has('message'))
    toastr.{{Session::get('m-class') ? Session::get('m-class'):'success'}}("{{Session::get('message')}}");
    @endif





    // Store company data globally for dynamic access
    window.companyData = {
        @auth('company')
        name: '{{ Auth::guard("company")->user()->name ?? "اسم الشركة" }}',
        address: '{{ Auth::guard("company")->user()->location ? Auth::guard("company")->user()->location . (Auth::guard("company")->user()->city ? "، " . Auth::guard("company")->user()->city : "") : "" }}',
        phone: '{{ Auth::guard("company")->user()->phone ?? "" }}',
        email: '{{ Auth::guard("company")->user()->email ?? "" }}',
        tax: '{{ Auth::guard("company")->user()->tax_number ?? "" }}',
        logo: '{{ $companyLogo ?? "" }}'
        @else
        name: 'اسم الشركة',
        address: '',
        phone: '',
        email: '',
        tax: '',
        logo: ''
        @endauth
    };

    function setProfessionalPrintHeader() {
        console.log('setProfessionalPrintHeader called - Data attributes are now set directly in HTML');
        const printableSection = document.getElementById('printable-section');

        if (!printableSection) {
            console.log('Error: printable-section not found');
            return;
        }

        // Check current data attributes
        console.log('Current data attributes:');
        console.log('data-company-name:', printableSection.getAttribute('data-company-name'));
        console.log('data-company-address:', printableSection.getAttribute('data-company-address'));
        console.log('data-company-phone:', printableSection.getAttribute('data-company-phone'));
        console.log('data-company-email:', printableSection.getAttribute('data-company-email'));
        console.log('data-company-tax:', printableSection.getAttribute('data-company-tax'));

        // Logo is now embedded in HTML, just ensure it's visible for print
        const logo = printableSection.querySelector('.print-company-logo');
        if (logo) {
            console.log('Logo found in HTML:', logo.src);
        } else {
            console.log('No logo found in HTML');
        }

        console.log('setProfessionalPrintHeader completed - Header should now be visible');
    }
    document.addEventListener('DOMContentLoaded', function () {
        const notificationDot = document.getElementById('notification-dot');
        const notificationsContainer = document.getElementById('notifications-container');

        fetch('{{ route('company.get-notifications') }}')
            .then(response => response.json())
            .then(data => {
                if (data.numOfUnreadNotifications) {
                    notificationDot.style.display = 'block';
                }

                let notificationsHtml = '';
                if (data.notifications.length) {
                    notificationsHtml = `
                    <div class="scroll-y mh-325px my-5 px-8">
                        ${data.notifications.map(notification => `
                            <div class="d-flex flex-stack py-4">
                                <div class="d-flex align-items-center me-2">
                                    <span class="w-30px badge badge-light-success me-4">جديد</span>
                                    <a href="#" class="notification-link text-gray-800 text-hover-primary fw-semibold" data-notification-id="${notification.id}" data-notification-url="${notification.data.url}">
                                        ${notification.data.message}
                                    </a>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
                } else {
                    notificationsHtml = `
                    <div class="d-flex flex-column px-9">
                        <div class="pt-10 pb-0">
                            <h3 class="text-dark text-center fw-bold">لا توجد إشعارات</h3>
                            <div class="text-center text-gray-600 fw-semibold pt-1">لا توجد إشعارات جديدة حالياً</div>
                        </div>
                    </div></br>
                `;
                }

                notificationsContainer.innerHTML = notificationsHtml;

                const scrollContainer = document.querySelector('.scroll-y');
                if (scrollContainer && scrollContainer.scrollHeight < scrollContainer.clientHeight) {
                    console.log(true);
                    scrollContainer.style.overflowY = 'hidden';
                } else {
                    console.log(false);
                    scrollContainer.style.overflowY = 'auto';
                }

                const notificationLinks = document.querySelectorAll('.notification-link');
                notificationLinks.forEach(link => {
                    link.addEventListener('click', function (event) {
                        event.preventDefault();

                        const notificationId = this.getAttribute('data-notification-id');
                        const url = this.getAttribute('data-notification-url');

                        fetch(`/company/notifications/${notificationId}/mark-as-read`, {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                                'Content-Type': 'application/json',
                            },
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    window.location.href = url;
                                }
                            });
                    });
                });
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
            });
    });

    
</script>
@yield('script')
</body>
</html>
