

<script src="<?php echo e(asset('/manager_assest/dist/assets/plugins/custom/datatables/datatables.bundle.js')); ?>"></script>

<script>
    var table = $('#datatable');
    var isPrinting = false;

    // begin first table
    table.DataTable({
        responsive: {
            details: {
                type: 'column',
                target: 'tr'
            },
            breakpoints: [
                { name: 'bigdesktop', width: Infinity },
                { name: 'meddesktop', width: 1480 },
                { name: 'desktop', width: 1024 },
                { name: 'tablet', width: 768 },
                { name: 'fablet', width: 480 },
                { name: 'phone', width: 320 }
            ]
        },
        processing: true,
        serverSide: true,
        ordering: false,
        searching: false, // Disable searching
        pageLength: 15,
        lengthMenu: [[10, 15, 25, 50, 100], [10, 15, 25, 50, 100]],
        footerCallback: function () {
            // Ensure footer is always visible
            $(this.api().table().footer()).show();
        },

        ajax: {
            url: '<?php echo e($ajaxRoute); ?>', // This will be passed from the blade file
            data: function (d) {
                // Add filter parameters - these will be customized per report
                <?php if(isset($filterParams)): ?>
                    <?php $__currentLoopData = $filterParams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $param): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        d.<?php echo e($param); ?> = $("#<?php echo e($param); ?>").val();
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
                
                // If printing, request all data
                if (isPrinting) {
                    d.length = -1; // Request all records
                    d.start = 0;
                }
            }
        },
        columns: [
            {
                data: null,
                name: 'sequence',
                orderable: false,
                searchable: false,
                render: function (data, type, row, meta) {
                    // Return sequence number for all types (display, print, export, etc.)
                    return meta.row + meta.settings._iDisplayStart + 1;
                },
                className: 'sequence-column text-center'
            },
            // Additional columns will be defined in each specific report
            <?php if(isset($reportColumns)): ?>
                <?php $__currentLoopData = $reportColumns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $column): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    {
                        data: '<?php echo e($column['data']); ?>', 
                        name: '<?php echo e($column['name']); ?>',
                        className: '<?php echo e($column['data']); ?>-column'
                    },
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        ],
        columnDefs: [
            {
                targets: 0, // Sequence column
                responsivePriority: 1, // Highest priority - never hide
                className: 'sequence-column text-center',
                orderable: false,
                searchable: false,
                width: '50px',
                render: function (data, type, row, meta) {
                    // Ensure sequence number is always visible
                    if (type === 'display' || type === 'print') {
                        return '<span class="sequence-number">' + (meta.row + meta.settings._iDisplayStart + 1) + '</span>';
                    }
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            {
                targets: [1, 2], // First two data columns
                responsivePriority: 2, // High priority - show on most devices
                className: 'priority-column'
            },
            {
                targets: '_all',
                responsivePriority: 3, // Lower priority for remaining columns
                className: 'data-column'
            }
        ],
        dom: 'Bfrtip', // Removed 'f' (filter/search) from DOM
        buttons: [],
        drawCallback: function(settings) {
            // Update all totals displays every time the table is drawn
            var api = this.api();
            var json = api.ajax.json();

            console.log('DrawCallback - Received totals:', json);
            console.log('Records in table:', api.data().length);

            if (json && json.totalCreditor !== undefined && json.totalDebtor !== undefined) {
                console.log('Updating totals - Creditor:', json.totalCreditor, 'Debtor:', json.totalDebtor, 'Records:', json.recordCount);

                // Parse the formatted numbers to calculate balance
                var creditorValue = parseFloat(json.totalCreditor.replace(/,/g, ''));
                var debtorValue = parseFloat(json.totalDebtor.replace(/,/g, ''));
                var balanceValue = debtorValue - creditorValue;
                var formattedBalance = balanceValue.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});

                // Update the print footer with current totals (filtered or unfiltered)
                $('#print-total-creditor').text(json.totalCreditor);
                $('#print-total-debtor').text(json.totalDebtor);

                // Update the display cards with current totals
                $('#display-total-creditor').text(json.totalCreditor);
                $('#display-total-debtor').text(json.totalDebtor);
                $('#display-total-balance').text(formattedBalance);

                // Update opening balance if provided by server, otherwise use calculated balance
                if (json.totalBalance !== undefined) {
                    $('#display-opening-balance').text(json.totalBalance);
                    console.log('Opening balance updated:', json.totalBalance);
                } else {
                    $('#display-opening-balance').text(formattedBalance);
                    console.log('Opening balance updated with calculated value:', formattedBalance);
                }

                console.log('All totals updated successfully - Balance:', formattedBalance);
            } else {
                console.log('No totals data received or undefined');
            }
        }
    });

    // Enhanced print function with comprehensive mobile support
    function printSection() {
        isPrinting = true;

        // Show loading indicator
        const printBtn = document.querySelector('button[onclick="printSection()"]');
        const originalText = printBtn.innerHTML;
        printBtn.innerHTML = 'جاري تحميل البيانات...';
        printBtn.disabled = true;

        // Store original responsive state
        const dataTable = table.DataTable();
        const originalResponsive = dataTable.responsive;
        const isMobile = window.innerWidth <= 768;

        // Clear current table data
        dataTable.clear().draw();

        // Reload with all data
        dataTable.ajax.reload(function(json) {
            // Comprehensive mobile print preparation
            if (isMobile) {
                // Force disable responsive behavior completely
                if (originalResponsive) {
                    originalResponsive.disable();
                }

                // Remove all responsive classes and show all columns
                $('#datatable').removeClass('dtr-inline collapsed');
                $('#datatable th, #datatable td').removeClass('dtr-hidden d-none d-sm-none d-md-none d-lg-none d-xl-none d-xxl-none hidden sr-only');
                $('#datatable th, #datatable td').show().css({
                    'display': 'table-cell',
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Remove responsive control elements
                $('#datatable .dtr-control').remove();
                $('#datatable .dtr-details').remove();

                // Force table layout for mobile print
                $('#datatable').css({
                    'table-layout': 'fixed',
                    'width': '100%',
                    'border-collapse': 'collapse'
                });

                // Optimize column widths for mobile
                const totalColumns = $('#datatable thead th').length;
                const sequenceWidth = '25px';
                const dataColumnWidth = `calc((100% - ${sequenceWidth}) / ${totalColumns - 1})`;

                $('#datatable th:first-child, #datatable td:first-child').css({
                    'width': sequenceWidth,
                    'max-width': sequenceWidth,
                    'min-width': sequenceWidth
                });

                $('#datatable th:not(:first-child), #datatable td:not(:first-child)').css({
                    'width': dataColumnWidth,
                    'max-width': 'none',
                    'min-width': '0'
                });
            } else {
                // Desktop/tablet print preparation
                if (originalResponsive && originalResponsive.hasHidden()) {
                    originalResponsive.recalc();
                    const hiddenColumns = dataTable.columns('.dtr-hidden');
                    hiddenColumns.nodes().to$().removeClass('dtr-hidden').show();
                }
            }

            // Add print-ready class to table
            $('#datatable').addClass('print-ready mobile-print-optimized');

            // Fix sequence numbers and ensure all content is visible
            setTimeout(function() {
                // Ensure sequence column is always visible
                $('#datatable th:first-child, #datatable td:first-child').show().css({
                    'display': 'table-cell',
                    'visibility': 'visible'
                });

                // Re-number all rows for continuous sequence in print
                $('#datatable tbody tr').each(function(index) {
                    const firstCell = $(this).find('td:first-child');
                    firstCell.text(index + 1);
                    firstCell.removeClass('dtr-hidden').show().css({
                        'display': 'table-cell',
                        'visibility': 'visible'
                    });
                });

                // Force all table elements to be visible
                $('#datatable *').css({
                    'display': function(index, currentDisplay) {
                        const tagName = this.tagName.toLowerCase();
                        if (tagName === 'th' || tagName === 'td') {
                            return 'table-cell';
                        } else if (tagName === 'tr') {
                            return 'table-row';
                        } else if (tagName === 'thead' || tagName === 'tfoot') {
                            return 'table-header-group';
                        } else if (tagName === 'tbody') {
                            return 'table-row-group';
                        } else if (tagName === 'table') {
                            return 'table';
                        }
                        return currentDisplay;
                    },
                    'visibility': 'visible',
                    'opacity': '1'
                });

                // Mobile-specific adjustments
                if (isMobile) {
                    // Ensure table fits mobile print area
                    $('#datatable').css({
                        'font-size': '6px',
                        'line-height': '1.1'
                    });

                    $('#datatable th, #datatable td').css({
                        'padding': '2px 1px',
                        'font-size': '6px',
                        'white-space': 'nowrap',
                        'overflow': 'visible',
                        'text-overflow': 'clip'
                    });
                }

                // Final column adjustment
                dataTable.columns.adjust();

                // Trigger print
                window.print();

                // Reset everything after print
                setTimeout(function() {
                    printBtn.innerHTML = originalText;
                    printBtn.disabled = false;
                    isPrinting = false;

                    // Remove print-ready classes
                    $('#datatable').removeClass('print-ready mobile-print-optimized');

                    // Re-enable responsive behavior
                    if (originalResponsive && isMobile) {
                        originalResponsive.enable();
                    }

                    // Reload normal pagination after printing
                    dataTable.ajax.reload(function() {
                        if (originalResponsive) {
                            originalResponsive.recalc();
                        }
                    });
                }, 1000); // Give time for print dialog to close

            }, isMobile ? 300 : 150); // Longer timeout for mobile devices
        });
    }

    // Clear all filters
    $('#clear-filters').on('click', function() {
        <?php if(isset($filterParams)): ?>
            <?php $__currentLoopData = $filterParams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $param): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                $('#<?php echo e($param); ?>').val('');
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
        table.DataTable().draw(true);
    });

    // Auto-refresh on filter changes
    <?php if(isset($filterParams)): ?>
        <?php $__currentLoopData = $filterParams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $param): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            $('#<?php echo e($param); ?>').on('change', function() {
                table.DataTable().draw(true);
            });
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>
</script>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\dasta\resources\views/company/reports/partials/unified-report-script.blade.php ENDPATH**/ ?>