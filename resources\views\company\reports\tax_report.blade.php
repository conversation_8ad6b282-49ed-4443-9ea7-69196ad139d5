@extends('company.layout.CompanyLayout')
@section('title')
    تقرير الضريبة
@endsection
@section('css')
    @include('company.reports.partials.unified-report-template')
    <style>
        /* Tax Report specific print styles */
        @media print {
            /* Hide the empty first column in tax report tables */
            .table th:first-child,
            .table td:first-child {
                display: none !important;
            }
            
            /* Ensure proper column alignment after hiding first column */
            .table th,
            .table td {
                text-align: center !important;
            }
        }
    </style>
@endsection

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">

                    </div>
                    <!--end::Card title-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <div class="fv-row row mb-15">
                        <div class="row" id="filter-form">
                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">من تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="from_date" id="from_date" class="form-control mb-2" value=""/>
                            </div>

                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">الى تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="to_date" id="to_date" class="form-control mb-2" value=""/>
                            </div>


                            <!--begin::Action buttons-->
                            <div class="col-md-3">
                                <div class="d-flex justify-content-start">
                                    <!--begin::Clear Filters Button-->
                                    <button type="button" id="clear-filters" class="btn btn-light">
                                        <i class="ki-duotone ki-cross fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        مسح الفلاتر
                                    </button>
                                    <!--end::Clear Filters Button-->
                                </div>
                            </div>
                            <!--end::Action buttons-->

                        </div>


                    </div>
                    <!--begin::Table-->
                    @can('tax_report.print')
                    <button onclick="printSection()" class="btn btn-success">طباعة</button>
                    @endcan

                    <div id="printable-section" data-report-title="تقرير الضريبة" data-report-date="{{ now()->format('Y-m-d H:i') }}">
                    
                        <!-- Company Information Header Box -->
                        <div class="company-header-box">
                            @if($companyLogo)
                                <img src="{{ $companyLogo }}" class="print-company-logo" alt="شعار الشركة" style="display: none;">
                            @endif

                            <!-- Company Information Section -->
                            <div class="company-info-section">
                                <div class="company-name">🏛️ {{ Auth::guard('company')->user()->name ?? 'اسم الشركة' }}</div>
                                @if(Auth::guard('company')->user()->location || Auth::guard('company')->user()->city)
                                    <div>📍 {{ Auth::guard('company')->user()->location ? Auth::guard('company')->user()->location . (Auth::guard('company')->user()->city ? '، ' . Auth::guard('company')->user()->city : '') : '' }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->phone)
                                    <div>📞 {{ Auth::guard('company')->user()->phone }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->email)
                                    <div>✉️ {{ Auth::guard('company')->user()->email }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->tax_number)
                                    <div>🏢 {{ Auth::guard('company')->user()->tax_number }}</div>
                                @endif
                            </div>

                            <!-- Report Title Section - Inside Company Box -->
                            <div class="report-title-box">
                                <div class="report-title">تقرير الضريبة</div>
                                <div class="report-date">تاريخ التقرير: {{ now()->format('Y-m-d H:i:s') }}</div>
                            </div>
                        </div>

                        <!-- Header Divider Line -->
                        <div class="header-divider"></div>

                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="static-table-1">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">ضريبة القيمة الممضافة على الايرادات</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المبلغ</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">التعديلات</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الضريبة</th>

                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">


                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold">  المبيعات الخاضعة لنسبة الضريبة 15% </td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-tax-15">{{$sales_tax_15}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-tax-15-resales">{{$sales_tax_15_resales}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-tax-15-amount">{{($sales_tax_15-$sales_tax_15_resales)*0.15}}</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold"> المبيعت لمواطنين"الخدمات الصحية" بدون ضريبة  </td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold">المبيعات المحلية الخاضعة لنسبة صفرية</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-zero-tax">{{$sales_zero_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-zero-tax-resales">{{$sales_zero_tax_resales}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-zero-tax-amount">{{$sales_zero_tax_amount}}</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                               <td class="text-gray-800 fs-5 fw-bold">الصادرات</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold">المبيعات المعفاة</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-free-tax">{{$sales_free_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-free-tax-resales">{{$sales_free_tax_resales}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-free-tax-amount">{{$sales_free_tax_amount}}</td>

                            </tr>

                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold"> المبيعات بدون ضريبة </td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-no-tax">{{$sales_no_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-no-tax-resales">{{$sales_no_tax_resales}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-sales-no-tax-amount">{{$sales_no_tax_amount}}</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold">اجمالي المبيعات</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-total-sales">{{$sales_tax_15+$sales_no_tax+$sales_zero_tax+$sales_free_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-total-sales-resales">{{$sales_tax_15_resales+$sales_no_tax_resales+$sales_zero_tax_resales+$sales_free_tax_resales}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-total-sales-tax">{{($sales_tax_15-$sales_tax_15_resales)*0.15}}</td>
                            </tr>


                        </tbody>
                    </table>
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="static-table-2">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الضريبة على المشتريات</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المبلغ</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">التعديلات</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الضريبة</th>

                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">


                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold">المشتريات الخاضعة لنسبة الضريبة 15 %</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-tax-15">{{$repurchase_tax_15}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-tax-15-repurchase">{{$repurchase_tax_15_repurchase}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-tax-15-amount">{{($repurchase_tax_15-$repurchase_tax_15_repurchase)*0.15}}</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold">الاستيراد الخاضع لضريبة القيمة المضافة التي تدفع في الجمارك </td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold">المشتريات الخاضعة لنسبة صفرية</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-zero-tax">{{$repurchase_zero_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-zero-tax-repurchase">{{$repurchase_zero_tax_repurchase}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-zero-tax-amount">{{$repurchase_zero_tax_amount}}</td>


                            </tr>

                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold">الاستيراد الخاضع لضريبة القيمة المضافة التي تدفع في الية الاحتساب العكسي</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-free-tax">{{$repurchase_free_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-free-tax-repurchase">{{$repurchase_free_tax_repurchase}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-free-tax-amount">{{$repurchase_free_tax_amount}}</td>

                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold">المشتريات بدون ضريبة </td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-no-tax">{{$repurchase_no_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-no-tax-repurchase">{{$repurchase_no_tax_repurchase}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-purchases-no-tax-amount">{{$repurchase_no_tax_amount}}</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold">اجمالي المشتريات</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-total-purchases">{{$repurchase_tax_15+$repurchase_no_tax+$repurchase_zero_tax+$repurchase_free_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-total-purchases-repurchase">{{$repurchase_tax_15_repurchase+$repurchase_no_tax_repurchase+$repurchase_zero_tax_repurchase+$repurchase_free_tax_repurchase}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-total-purchases-tax">{{($repurchase_tax_15-$repurchase_tax_15_repurchase)*0.15}}</td>

                            </tr>






                        </tbody>
                    </table>
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="static-table-3">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الضريبة على المصروفات</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المبلغ</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">التعديلات</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">الضريبة</th>

                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">


                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold">المصروفات الخاضعة لنسبة اساسية 15 %</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-expenses-tax-15">{{$expenses_tax_15}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-expenses-tax-15-tax">{{$expenses_tax_15_tax}}</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold">المصروفات بدون ضريبة</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-expenses-no-tax">{{$expenses_no_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>


                            </tr>

                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold"> المصروفات الخاضعة لضريبة صفرية</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-expenses-zero-tax">{{$expenses_zero_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>


                            </tr>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>
                                <td class="text-gray-800 fs-5 fw-bold"> المصروفات الخاضعة لضريبة معفاة</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-expenses-free-tax">{{$expenses_free_tax}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>
                                <td class="text-gray-800 fs-5 fw-bold">0</td>


                            </tr>

                            <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                <th class="w-10px pe-2">

                                </th>
                                <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">إجمالي ضريبة القيمة المضافة المستحقة</th>
                                <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المبلغ</th>
                                <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;"></th>
                                <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-final-tax">{{(($sales_tax_15-$sales_tax_15_resales)*0.15)-((($repurchase_tax_15-$repurchase_tax_15_repurchase)*0.15)+$expenses_tax_15_tax)}}</th>

                            </tr>


                        </tbody>
                    </table>
                    </div>
                    <!--end::Table-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Content container-->
    </div>




@endsection
@section('script')
    <script>
        // Simple print function for static table (no DataTables)
        function printSection() {
            // Show loading indicator
            const printBtn = document.querySelector('button[onclick="printSection()"]');
            if (printBtn) {
                const originalText = printBtn.innerHTML;
                printBtn.innerHTML = 'جاري الطباعة...';
                printBtn.disabled = true;

                // Trigger print after a short delay
                setTimeout(function() {
                    window.print();

                    // Reset button
                    printBtn.innerHTML = originalText;
                    printBtn.disabled = false;
                }, 100);
            } else {
                // Fallback if button not found
                window.print();
            }
        }

        // Real-time filtering functionality
        function updateTotals() {
            const fromDate = $('#from_date').val();
            const toDate = $('#to_date').val();

            $.ajax({
                url: '{{ route('company.tax_report') }}',
                method: 'GET',
                data: {
                    from_date: fromDate,
                    to_date: toDate
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // Update ALL expenses values
                    $('#display-expenses-tax-15').text(response.expensesTax15);
                    $('#display-expenses-tax-15-tax').text(response.expensesTax15Tax);
                    $('#display-expenses-no-tax').text(response.expensesNoTax);
                    $('#display-expenses-zero-tax').text(response.expensesZeroTax);
                    $('#display-expenses-free-tax').text(response.expensesFreeTax);

                    // Update ALL sales values (including resales)
                    $('#display-sales-tax-15').text(response.salesTax15);
                    $('#display-sales-tax-15-resales').text(response.salesTax15Resales);
                    $('#display-sales-tax-15-amount').text(response.salesTax15Amount);
                    $('#display-sales-no-tax').text(response.salesNoTax);
                    $('#display-sales-no-tax-resales').text(response.salesNoTaxResales);
                    $('#display-sales-no-tax-amount').text(response.salesNoTaxAmount);
                    $('#display-sales-zero-tax').text(response.salesZeroTax);
                    $('#display-sales-zero-tax-resales').text(response.salesZeroTaxResales);
                    $('#display-sales-zero-tax-amount').text(response.salesZeroTaxAmount);
                    $('#display-sales-free-tax').text(response.salesFreeTax);
                    $('#display-sales-free-tax-resales').text(response.salesFreeTaxResales);
                    $('#display-sales-free-tax-amount').text(response.salesFreeTaxAmount);

                    // Update ALL purchases values (including repurchases)
                    $('#display-purchases-tax-15').text(response.purchasesTax15);
                    $('#display-purchases-tax-15-repurchase').text(response.purchasesTax15Repurchase);
                    $('#display-purchases-tax-15-amount').text(response.purchasesTax15Amount);
                    $('#display-purchases-no-tax').text(response.purchasesNoTax);
                    $('#display-purchases-no-tax-repurchase').text(response.purchasesNoTaxRepurchase);
                    $('#display-purchases-no-tax-amount').text(response.purchasesNoTaxAmount);
                    $('#display-purchases-zero-tax').text(response.purchasesZeroTax);
                    $('#display-purchases-zero-tax-repurchase').text(response.purchasesZeroTaxRepurchase);
                    $('#display-purchases-zero-tax-amount').text(response.purchasesZeroTaxAmount);
                    $('#display-purchases-free-tax').text(response.purchasesFreeTax);
                    $('#display-purchases-free-tax-repurchase').text(response.purchasesFreeRepurchase);
                    $('#display-purchases-free-tax-amount').text(response.purchasesFreeTaxAmount);

                    // Update totals (including resales/repurchases)
                    $('#display-total-sales').text(response.totalSales);
                    $('#display-total-sales-resales').text(response.totalSalesResales);
                    $('#display-total-purchases').text(response.totalPurchases);
                    $('#display-total-purchases-repurchase').text(response.totalPurchasesRepurchase);
                    $('#display-total-sales-tax').text(response.totalSalesTax);
                    $('#display-total-purchases-tax').text(response.totalPurchasesTax);
                    $('#display-final-tax').text(response.finalTax);
                },
                error: function(xhr, status, error) {
                    console.error('Error updating totals:', error);
                }
            });
        }

        // Auto-refresh on date changes
        $('#from_date, #to_date').on('change', function() {
            updateTotals();
        });

        // Clear all filters
        $('#clear-filters').on('click', function() {
            $('#from_date').val('');
            $('#to_date').val('');
            updateTotals();
        });
    </script>
@endsection


