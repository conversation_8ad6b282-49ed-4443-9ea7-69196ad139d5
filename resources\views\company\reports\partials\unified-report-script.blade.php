{{-- 
    UNIFIED REPORT JAVASCRIPT TEMPLATE
    Extracted from sales_report.blade.php - Gold Standard Architecture
    DataTables AJAX with sophisticated print functionality
    Sequential numbering, totals calculation, and responsive design
--}}

<script src="{{asset('/manager_assest/dist/assets/plugins/custom/datatables/datatables.bundle.js')}}"></script>

<script>
    var table = $('#datatable');
    var isPrinting = false;

    // begin first table
    table.DataTable({
        responsive: {
            details: {
                type: 'column',
                target: 'tr'
            }
        },
        processing: true,
        serverSide: true,
        ordering: false,
        searching: false, // Disable searching
        pageLength: 15,
        lengthMenu: [[10, 15, 25, 50, 100], [10, 15, 25, 50, 100]],
        footerCallback: function () {
            // Ensure footer is always visible
            $(this.api().table().footer()).show();
        },

        ajax: {
            url: '{{ $ajaxRoute }}', // This will be passed from the blade file
            data: function (d) {
                // Add filter parameters - these will be customized per report
                @if(isset($filterParams))
                    @foreach($filterParams as $param)
                        d.{{ $param }} = $("#{{ $param }}").val();
                    @endforeach
                @endif
                
                // If printing, request all data
                if (isPrinting) {
                    d.length = -1; // Request all records
                    d.start = 0;
                }
            }
        },
        columns: [
            {
                data: null,
                name: 'sequence',
                orderable: false,
                searchable: false,
                render: function (data, type, row, meta) {
                    // Return sequence number for all types (display, print, export, etc.)
                    return meta.row + meta.settings._iDisplayStart + 1;
                },
                className: 'sequence-column text-center'
            },
            // Additional columns will be defined in each specific report
            @if(isset($reportColumns))
                @foreach($reportColumns as $column)
                    {
                        data: '{{ $column['data'] }}', 
                        name: '{{ $column['name'] }}',
                        className: '{{ $column['data'] }}-column'
                    },
                @endforeach
            @endif
        ],
        columnDefs: [
            {
                targets: 0, // Sequence column
                responsivePriority: 1, // Highest priority - never hide
                className: 'sequence-column text-center',
                orderable: false,
                searchable: false
            },
            {
                targets: '_all',
                responsivePriority: 2 // Lower priority for other columns
            }
        ],
        dom: 'Bfrtip', // Removed 'f' (filter/search) from DOM
        buttons: [],
        drawCallback: function(settings) {
            // Update all totals displays every time the table is drawn
            var api = this.api();
            var json = api.ajax.json();

            console.log('DrawCallback - Received totals:', json);
            console.log('Records in table:', api.data().length);

            if (json && json.totalCreditor !== undefined && json.totalDebtor !== undefined) {
                console.log('Updating totals - Creditor:', json.totalCreditor, 'Debtor:', json.totalDebtor, 'Records:', json.recordCount);

                // Parse the formatted numbers to calculate balance
                var creditorValue = parseFloat(json.totalCreditor.replace(/,/g, ''));
                var debtorValue = parseFloat(json.totalDebtor.replace(/,/g, ''));
                var balanceValue = debtorValue - creditorValue;
                var formattedBalance = balanceValue.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});

                // Update the print footer with current totals (filtered or unfiltered)
                $('#print-total-creditor').text(json.totalCreditor);
                $('#print-total-debtor').text(json.totalDebtor);

                // Update the display cards with current totals
                $('#display-total-creditor').text(json.totalCreditor);
                $('#display-total-debtor').text(json.totalDebtor);
                $('#display-total-balance').text(formattedBalance);

                // Update opening balance if provided by server, otherwise use calculated balance
                if (json.totalBalance !== undefined) {
                    $('#display-opening-balance').text(json.totalBalance);
                    console.log('Opening balance updated:', json.totalBalance);
                } else {
                    $('#display-opening-balance').text(formattedBalance);
                    console.log('Opening balance updated with calculated value:', formattedBalance);
                }

                console.log('All totals updated successfully - Balance:', formattedBalance);
            } else {
                console.log('No totals data received or undefined');
            }
        }
    });

    // Print function that loads all data and handles responsive tables
    function printSection() {
        isPrinting = true;

        // Show loading indicator
        const printBtn = document.querySelector('button[onclick="printSection()"]');
        const originalText = printBtn.innerHTML;
        printBtn.innerHTML = 'جاري تحميل البيانات...';
        printBtn.disabled = true;

        // Store original responsive state
        const dataTable = table.DataTable();
        const originalResponsive = dataTable.responsive;

        // Clear current table data
        dataTable.clear().draw();

        // Reload with all data
        dataTable.ajax.reload(function(json) {
            // Force all columns to be visible for printing
            if (originalResponsive && originalResponsive.hasHidden()) {
                // Temporarily disable responsive and show all columns
                originalResponsive.recalc();

                // Force show all hidden columns
                const hiddenColumns = dataTable.columns('.dtr-hidden');
                hiddenColumns.nodes().to$().removeClass('dtr-hidden').show();

                // Add print-ready class to table
                $('#datatable').addClass('print-ready');
            }

            // Fix sequence numbers and responsive issues for print
            setTimeout(function() {
                // Ensure sequence column is visible
                $('#datatable th:first-child, #datatable td:first-child').show();

                // Re-number all rows for continuous sequence in print
                $('#datatable tbody tr').each(function(index) {
                    const firstCell = $(this).find('td:first-child');
                    firstCell.text(index + 1);
                    firstCell.removeClass('dtr-hidden').show();
                });

                // Force responsive recalculation for mobile devices
                if (window.innerWidth <= 768) {
                    dataTable.columns.adjust();
                    dataTable.responsive.recalc();
                }

                // Trigger print
                window.print();

                // Reset button and printing flag
                printBtn.innerHTML = originalText;
                printBtn.disabled = false;
                isPrinting = false;

                // Remove print-ready class
                $('#datatable').removeClass('print-ready');

                // Reload normal pagination after printing
                dataTable.ajax.reload(function() {
                    // Restore responsive behavior
                    if (originalResponsive) {
                        originalResponsive.recalc();
                    }
                });
            }, 150); // Slightly longer timeout for mobile devices
        });
    }

    // Clear all filters
    $('#clear-filters').on('click', function() {
        @if(isset($filterParams))
            @foreach($filterParams as $param)
                $('#{{ $param }}').val('');
            @endforeach
        @endif
        table.DataTable().draw(true);
    });

    // Auto-refresh on filter changes
    @if(isset($filterParams))
        @foreach($filterParams as $param)
            $('#{{ $param }}').on('change', function() {
                table.DataTable().draw(true);
            });
        @endforeach
    @endif
</script>
