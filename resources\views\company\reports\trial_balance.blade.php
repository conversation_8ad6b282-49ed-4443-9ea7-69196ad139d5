@extends('company.layout.CompanyLayout')
@section('title')
    تقرير ميزان المراجعة العامة
@endsection
@section('css')
    @include('company.reports.partials.unified-report-template')
    <style>
        /* Trial Balance Report specific print styles */
        @media print {
            /* Hide the empty first column in trial balance report tables */
            .table th:first-child,
            .table td:first-child {
                display: none !important;
            }
            
            /* Ensure proper column alignment after hiding first column */
            .table th,
            .table td {
                text-align: center !important;
            }
            
            /* Hide the green totals row in print */
            .table tbody tr:last-child {
                display: none !important;
            }
            
            /* Force totals row alignment to match table */
            .table tfoot.totals-row td,
            .table .print-only-totals td {
                text-align: center !important;
                vertical-align: middle !important;
            }
            
            /* Ensure print-only totals are visible in print */
            .print-only-totals {
                display: table-footer-group !important;
                visibility: visible !important;
            }
            
            .print-only-totals tr {
                display: table-row !important;
                visibility: visible !important;
            }
            
            .print-only-totals td {
                display: table-cell !important;
                visibility: visible !important;
            }
            
            /* Specific rules for trial balance table */
            #static-table .print-only-totals {
                display: table-footer-group !important;
                visibility: visible !important;
            }
            
            #static-table .print-only-totals tr {
                display: table-row !important;
                visibility: visible !important;
            }
            
            #static-table .print-only-totals td {
                display: table-cell !important;
                visibility: visible !important;
            }
        }
    </style>
@endsection

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">

                    </div>
                    <!--end::Card title-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <div class="fv-row row mb-15">
                        <div class="row" id="filter-form">
                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">من تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="from_date" id="from_date" class="form-control mb-2" value=""/>
                            </div>

                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">الى تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="to_date" id="to_date" class="form-control mb-2" value=""/>
                            </div>

                            <!--begin::Action buttons-->
                            <div class="col-md-3">
                                <div class="d-flex justify-content-start">
                                    <!--begin::Clear Filters Button-->
                                    <button type="button" id="clear-filters" class="btn btn-light">
                                        <i class="ki-duotone ki-cross fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        مسح الفلاتر
                                    </button>
                                    <!--end::Clear Filters Button-->
                                </div>
                            </div>
                            <!--end::Action buttons-->

                        </div>

                    </div>
                    <!--begin::Table-->
                    @can('trial_balance.print')
                    <button onclick="printSection()" class="btn btn-success">طباعة</button>
                    @endcan

                    <div id="printable-section" data-report-title="تقرير ميزان المراجعة العامة" data-report-date="{{ now()->format('Y-m-d H:i') }}">
                    
                        <!-- Company Information Header Box -->
                        <div class="company-header-box">
                            @if($companyLogo)
                                <img src="{{ $companyLogo }}" class="print-company-logo" alt="شعار الشركة" style="display: none;">
                            @endif

                            <!-- Company Information Section -->
                            <div class="company-info-section">
                                <div class="company-name">🏛️ {{ Auth::guard('company')->user()->name ?? 'اسم الشركة' }}</div>
                                @if(Auth::guard('company')->user()->location || Auth::guard('company')->user()->city)
                                    <div>📍 {{ Auth::guard('company')->user()->location ? Auth::guard('company')->user()->location . (Auth::guard('company')->user()->city ? '، ' . Auth::guard('company')->user()->city : '') : '' }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->phone)
                                    <div>📞 {{ Auth::guard('company')->user()->phone }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->email)
                                    <div>✉️ {{ Auth::guard('company')->user()->email }}</div>
                                @endif
                                @if(Auth::guard('company')->user()->tax_number)
                                    <div>🏢 {{ Auth::guard('company')->user()->tax_number }}</div>
                                @endif
                            </div>

                            <!-- Report Title Section - Inside Company Box -->
                            <div class="report-title-box">
                                <div class="report-title">تقرير ميزان المراجعة العامة</div>
                                <div class="report-date">تاريخ التقرير: {{ now()->format('Y-m-d H:i:s') }}</div>
                            </div>
                        </div>

                        <!-- Header Divider Line -->
                        <div class="header-divider"></div>

                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="static-table">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">اسم الحساب</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">طبيعة الحساب</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">حركات مدينة </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">حركات دائنة</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">نهائى مدين</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">نهائي دائن</th>

                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">

                        @foreach($mains as $main)
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold">{{$main['name']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold">{{$main['type']=='debtor'?'مدين':"دائن"}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-debtor-{{$main['id']}}">{{$main['debtor']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-creditor-{{$main['id']}}">{{$main['creditor']}}</td>
                                @if($main['debtor'] >= $main['creditor'])
                                <td class="text-gray-800 fs-5 fw-bold" id="main-net-debtor-{{$main['id']}}">{{$main['debtor'] - $main['creditor']}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-net-creditor-{{$main['id']}}">0</td>
                                @else

                                <td class="text-gray-800 fs-5 fw-bold" id="main-net-debtor-{{$main['id']}}">0</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-net-creditor-{{$main['id']}}">{{abs($main['debtor'] - $main['creditor'])}}</td>

                                @endif
                            </tr>

                        @endforeach
                        <tr class="odd">
                            <td class="dtr-control">
                                <div class="form-check form-check-sm form-check-custom form-check-solid">

                                </div>
                            </td>
                            <td class="text-gray-800 fs-5 fw-bold">العملاء</td>
                            <td class="text-gray-800 fs-5 fw-bold">مدين</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-client-debtor">{{$client_debtor}}</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-client-creditor">{{$client_creditor}}</td>
                            @if($client_debtor >= $client_creditor)
                                <td class="text-gray-800 fs-5 fw-bold" id="display-client-net-debtor">{{$client_debtor - $client_creditor}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-client-net-creditor">0</td>
                            @else

                                <td class="text-gray-800 fs-5 fw-bold" id="display-client-net-debtor">0</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-client-net-creditor">{{abs($client_debtor - $client_creditor)}}</td>

                            @endif




                        </tr>
                        <tr class="odd">
                            <td class="dtr-control">
                                <div class="form-check form-check-sm form-check-custom form-check-solid">

                                </div>
                            </td>
                            <td class="text-gray-800 fs-5 fw-bold">الموردين</td>
                            <td class="text-gray-800 fs-5 fw-bold">دائن</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-debtor">{{$supplier_debtor}}</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-creditor">{{$supplier_creditor}}</td>
                            @if($supplier_debtor >= $supplier_creditor)
                                <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-net-debtor">{{$supplier_debtor - $supplier_creditor}}</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-net-creditor">0</td>
                            @else

                                <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-net-debtor">0</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-net-creditor">{{abs($supplier_debtor - $supplier_creditor)}}</td>

                            @endif





                        </tr>


                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المجموع</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;"></th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-total-debtor">{{$debtor}}</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-total-creditor">{{$creditor}} </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-net-debtor">{{$net_debtor}}</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-net-creditor">{{$net_creditor}}</th>


                        </tr>


                        </tbody>
                        
                        <!-- Print-only totals footer - Hidden on screen, visible only in print -->
                        <tfoot class="print-only-totals">
                            <tr class="totals-row">
                                <td colspan="2" style="text-align: center; font-weight: bold;">المجموع</td>
                                <td id="print-total-debtor" style="text-align: center; font-weight: bold; vertical-align: middle;">{{$debtor}}</td>
                                <td id="print-total-creditor" style="text-align: center; font-weight: bold; vertical-align: middle;">{{$creditor}}</td>
                                <td id="print-net-debtor" style="text-align: center; font-weight: bold; vertical-align: middle;">{{$net_debtor}}</td>
                                <td id="print-net-creditor" style="text-align: center; font-weight: bold; vertical-align: middle;">{{$net_creditor}}</td>
                            </tr>
                        </tfoot>
                    </table>
                    <!--end::Table-->
                    </div>
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Content container-->
    </div>




@endsection
@section('script')

<script>
    // Simple print function for static table
    function printSection() {
        // Show loading indicator
        const printBtn = document.querySelector('button[onclick="printSection()"]');
        if (printBtn) {
            const originalText = printBtn.innerHTML;
            printBtn.innerHTML = 'جاري الطباعة...';
            printBtn.disabled = true;

            // Trigger print after a short delay
            setTimeout(function() {
                window.print();

                // Reset button
                printBtn.innerHTML = originalText;
                printBtn.disabled = false;
            }, 100);
        } else {
            // Fallback if button not found
            window.print();
        }
    }

    // Real-time filtering functionality
    function updateTotals() {
        const fromDate = $('#from_date').val();
        const toDate = $('#to_date').val();

        $.ajax({
            url: '{{ route('company.trial_balance') }}',
            method: 'GET',
            data: {
                from_date: fromDate,
                to_date: toDate
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('Trial Balance Response:', response);
                // Update screen totals
                $('#display-total-creditor').text(response.totalCreditor);
                $('#display-total-debtor').text(response.totalDebtor);
                $('#display-net-creditor').text(response.netCreditor);
                $('#display-net-debtor').text(response.netDebtor);
                
                // Update print totals
                $('#print-total-creditor').text(response.totalCreditor);
                $('#print-total-debtor').text(response.totalDebtor);
                $('#print-net-creditor').text(response.netCreditor);
                $('#print-net-debtor').text(response.netDebtor);

                // Update client values
                $('#display-client-creditor').text(response.clientCreditor);
                $('#display-client-debtor').text(response.clientDebtor);

                // Calculate and update client net values
                var clientDebtor = parseFloat(response.clientDebtor.replace(/,/g, ''));
                var clientCreditor = parseFloat(response.clientCreditor.replace(/,/g, ''));
                if (clientDebtor >= clientCreditor) {
                    $('#display-client-net-debtor').text((clientDebtor - clientCreditor).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                    $('#display-client-net-creditor').text('0.00');
                } else {
                    $('#display-client-net-debtor').text('0.00');
                    $('#display-client-net-creditor').text(Math.abs(clientDebtor - clientCreditor).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                }

                // Update supplier values
                $('#display-supplier-creditor').text(response.supplierCreditor);
                $('#display-supplier-debtor').text(response.supplierDebtor);

                // Calculate and update supplier net values
                var supplierDebtor = parseFloat(response.supplierDebtor.replace(/,/g, ''));
                var supplierCreditor = parseFloat(response.supplierCreditor.replace(/,/g, ''));
                if (supplierDebtor >= supplierCreditor) {
                    $('#display-supplier-net-debtor').text((supplierDebtor - supplierCreditor).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                    $('#display-supplier-net-creditor').text('0.00');
                } else {
                    $('#display-supplier-net-debtor').text('0.00');
                    $('#display-supplier-net-creditor').text(Math.abs(supplierDebtor - supplierCreditor).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                }

                // Update ALL main accounts (sub accounts) rows
                if (response.mainAccounts && response.mainAccounts.length > 0) {
                    response.mainAccounts.forEach(function(account) {
                        $('#main-debtor-' + account.id).text(account.debtor);
                        $('#main-creditor-' + account.id).text(account.creditor);
                        $('#main-net-debtor-' + account.id).text(account.net_debtor);
                        $('#main-net-creditor-' + account.id).text(account.net_creditor);
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Trial Balance Error:', error);
                console.error('Response:', xhr.responseText);
            }
        });
    }

    // Auto-refresh on date changes
    $('#from_date, #to_date').on('change', function() {
        updateTotals();
    });

    // Clear all filters
    $('#clear-filters').on('click', function() {
        $('#from_date').val('');
        $('#to_date').val('');
        updateTotals();
    });
</script>

@endsection


