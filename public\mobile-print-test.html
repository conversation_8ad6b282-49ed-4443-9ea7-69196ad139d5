<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Mobile Print Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-info { background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .device-info { background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px; }
        
        /* Mobile Print CSS - Only affects mobile devices */
        @media print and (max-width: 768px) {
            .table {
                width: 100% !important;
                table-layout: fixed !important;
                border-collapse: collapse !important;
                font-size: 8px !important;
            }
            
            .table th, .table td {
                padding: 3px 2px !important;
                font-size: 7px !important;
                border: 1px solid #ddd !important;
                word-wrap: break-word !important;
                overflow: visible !important;
            }
            
            .table th:first-child, .table td:first-child {
                width: 25px !important;
                max-width: 25px !important;
                min-width: 25px !important;
                text-align: center !important;
            }
        }
        
        /* Desktop Print CSS - Only affects desktop/tablet */
        @media print and (min-width: 769px) {
            .table {
                font-size: 10px !important;
                border-collapse: collapse !important;
            }
            
            .table th, .table td {
                padding: 8px 6px !important;
                font-size: 10px !important;
                border: 1px solid #ddd !important;
            }
        }
        
        /* General print styles */
        @media print {
            body * { visibility: hidden; }
            #printable-section, #printable-section * { visibility: visible; }
            #printable-section { position: relative; width: 100%; margin: 0; padding: 0; }
            
            .table {
                width: 100% !important;
                border-collapse: collapse !important;
            }
            
            .table th, .table td {
                display: table-cell !important;
                visibility: visible !important;
                border: 1px solid #ddd !important;
                text-align: center !important;
            }
        }
        
        /* Screen styles */
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { padding: 8px; border: 1px solid #ddd; text-align: center; }
        .table th { background: #f5f5f5; font-weight: bold; }
    </style>
</head>
<body>
    <div class="test-info">
        <h2>Mobile Print Test - اختبار طباعة الموبايل</h2>
        <p><strong>Instructions:</strong></p>
        <ul>
            <li>On Desktop: Print should show normal size (10px font)</li>
            <li>On Mobile: Print should show compact size (7-8px font) with all columns visible</li>
            <li>Use browser dev tools to simulate mobile devices</li>
        </ul>
    </div>
    
    <div class="device-info">
        <p><strong>Current Screen Width:</strong> <span id="screen-width"></span>px</p>
        <p><strong>Device Type:</strong> <span id="device-type"></span></p>
        <p><strong>Expected Print Behavior:</strong> <span id="print-behavior"></span></p>
    </div>
    
    <button onclick="testPrint()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
        🖨️ Test Print - اختبار الطباعة
    </button>
    
    <div id="printable-section">
        <h3>Sample Report - تقرير تجريبي</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>التاريخ</th>
                    <th>التفاصيل</th>
                    <th>العميل</th>
                    <th>مدين</th>
                    <th>دائن</th>
                    <th>الحساب</th>
                </tr>
            </thead>
            <tbody>
                <tr><td>1</td><td>2024-01-15</td><td>بيع منتجات</td><td>عميل تجريبي</td><td>1,500.00</td><td>0.00</td><td>حساب المبيعات</td></tr>
                <tr><td>2</td><td>2024-01-16</td><td>خدمات استشارية</td><td>شركة ABC</td><td>2,300.50</td><td>0.00</td><td>حساب الخدمات</td></tr>
                <tr><td>3</td><td>2024-01-17</td><td>مرتجع مبيعات</td><td>عميل آخر</td><td>0.00</td><td>450.75</td><td>حساب المرتجعات</td></tr>
                <tr><td>4</td><td>2024-01-18</td><td>بيع معدات</td><td>مؤسسة التقنية</td><td>5,200.00</td><td>0.00</td><td>حساب المعدات</td></tr>
                <tr><td>5</td><td>2024-01-19</td><td>خصم تجاري</td><td>عميل مميز</td><td>0.00</td><td>125.25</td><td>حساب الخصومات</td></tr>
            </tbody>
            <tfoot>
                <tr style="background: #f0f0f0; font-weight: bold;">
                    <td colspan="4">المجموع</td>
                    <td>9,000.50</td>
                    <td>576.00</td>
                    <td>ـــــ</td>
                </tr>
            </tfoot>
        </table>
    </div>

    <script>
        function updateDeviceInfo() {
            const width = window.innerWidth;
            const widthSpan = document.getElementById('screen-width');
            const deviceSpan = document.getElementById('device-type');
            const behaviorSpan = document.getElementById('print-behavior');
            
            widthSpan.textContent = width;
            
            if (width <= 480) {
                deviceSpan.textContent = 'Extra Small Mobile';
                behaviorSpan.textContent = 'Ultra compact print (6px font)';
            } else if (width <= 768) {
                deviceSpan.textContent = 'Mobile';
                behaviorSpan.textContent = 'Compact print (7-8px font)';
            } else if (width <= 1024) {
                deviceSpan.textContent = 'Tablet';
                behaviorSpan.textContent = 'Normal print (10px font)';
            } else {
                deviceSpan.textContent = 'Desktop';
                behaviorSpan.textContent = 'Normal print (10px font)';
            }
        }
        
        function testPrint() {
            // Show current device info before printing
            updateDeviceInfo();
            
            // Add a small delay to ensure styles are applied
            setTimeout(function() {
                window.print();
            }, 100);
        }
        
        // Update device info on load and resize
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
    </script>
</body>
</html>
