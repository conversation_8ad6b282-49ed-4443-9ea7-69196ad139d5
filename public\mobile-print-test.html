<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Mobile Print Test - اختبار طباعة الموبايل</title>
    
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .info { background: #e3f2fd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .device-info { background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px; }
        
        /* Desktop Print - Original Styles (unchanged) */
        @media print and (min-width: 769px) {
            .table {
                border-collapse: collapse !important;
                width: 100% !important;
                font-size: 10px !important;
                background: #ffffff !important;
                border: 1px solid #bdc3c7 !important;
            }
            
            .table thead th {
                background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
                color: #ffffff !important;
                font-weight: 600 !important;
                padding: 14px 10px !important;
                font-size: 11px !important;
            }
            
            .table tbody td {
                padding: 12px 10px !important;
                font-size: 10px !important;
                text-align: center !important;
            }
        }
        
        /* Mobile Print - New Optimized Styles */
        @media print and (max-width: 768px) {
            .table {
                width: 100% !important;
                table-layout: fixed !important;
                border-collapse: collapse !important;
                font-size: 8px !important;
                background: #ffffff !important;
                border: 1px solid #bdc3c7 !important;
            }
            
            .table th,
            .table td {
                padding: 3px 2px !important;
                font-size: 7px !important;
                line-height: 1.2 !important;
                border: 1px solid #ddd !important;
                text-align: center !important;
                word-wrap: break-word !important;
                white-space: nowrap !important;
                display: table-cell !important;
                visibility: visible !important;
            }
            
            .table th:first-child,
            .table td:first-child {
                width: 25px !important;
                max-width: 25px !important;
                min-width: 25px !important;
                padding: 2px 1px !important;
                font-size: 7px !important;
                background: #ecf0f1 !important;
                font-weight: 600 !important;
            }
            
            .table thead th {
                background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%) !important;
                color: #ffffff !important;
                font-weight: 600 !important;
                font-size: 7px !important;
                padding: 3px 2px !important;
            }
        }
        
        /* Extra Small Mobile */
        @media print and (max-width: 480px) {
            .table { font-size: 6px !important; }
            .table th, .table td { padding: 2px 1px !important; font-size: 6px !important; }
            .table th:first-child, .table td:first-child { width: 20px !important; font-size: 6px !important; }
        }
        
        /* General print styles */
        @media print {
            body * { visibility: hidden; }
            #printable-section, #printable-section * { visibility: visible; }
            #printable-section { position: relative; width: 100%; margin: 0; padding: 0; }
        }
        
        /* Screen styles */
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { padding: 8px; border: 1px solid #ddd; text-align: center; }
        .table th { background: #f5f5f5; font-weight: bold; }
        .btn { background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="info">
        <h2>🖨️ Mobile Print Test - اختبار طباعة الموبايل</h2>
        <p><strong>Test Instructions - تعليمات الاختبار:</strong></p>
        <ul>
            <li><strong>Desktop (769px+):</strong> Print shows normal size (10-11px font) - unchanged</li>
            <li><strong>Mobile (≤768px):</strong> Print shows compact size (7-8px font) with ALL columns visible</li>
            <li><strong>Small Mobile (≤480px):</strong> Ultra-compact (6px font) with ALL columns visible</li>
            <li>Use browser dev tools (F12) to simulate mobile devices</li>
        </ul>
    </div>
    
    <div class="device-info">
        <p><strong>Current Screen:</strong> <span id="screen-width"></span>px</p>
        <p><strong>Device Type:</strong> <span id="device-type"></span></p>
        <p><strong>Expected Print:</strong> <span id="print-behavior"></span></p>
    </div>
    
    <button onclick="testPrint()" class="btn">
        🖨️ Test Print - اختبار الطباعة
    </button>
    
    <div id="printable-section">
        <h3>Sample Report - تقرير تجريبي</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>التاريخ</th>
                    <th>التفاصيل</th>
                    <th>العميل</th>
                    <th>مدين</th>
                    <th>دائن</th>
                    <th>الحساب</th>
                </tr>
            </thead>
            <tbody>
                <tr><td>1</td><td>2024-01-15</td><td>بيع منتجات</td><td>عميل تجريبي</td><td>1,500.00</td><td>0.00</td><td>حساب المبيعات</td></tr>
                <tr><td>2</td><td>2024-01-16</td><td>خدمات استشارية</td><td>شركة ABC</td><td>2,300.50</td><td>0.00</td><td>حساب الخدمات</td></tr>
                <tr><td>3</td><td>2024-01-17</td><td>مرتجع مبيعات</td><td>عميل آخر</td><td>0.00</td><td>450.75</td><td>حساب المرتجعات</td></tr>
                <tr><td>4</td><td>2024-01-18</td><td>بيع معدات</td><td>مؤسسة التقنية</td><td>5,200.00</td><td>0.00</td><td>حساب المعدات</td></tr>
                <tr><td>5</td><td>2024-01-19</td><td>خصم تجاري</td><td>عميل مميز</td><td>0.00</td><td>125.25</td><td>حساب الخصومات</td></tr>
            </tbody>
            <tfoot>
                <tr style="background: #2c3e50; color: white; font-weight: bold;">
                    <td colspan="4">المجموع</td>
                    <td>9,000.50</td>
                    <td>576.00</td>
                    <td>ـــــ</td>
                </tr>
            </tfoot>
        </table>
    </div>

    <script>
        function updateDeviceInfo() {
            const width = window.innerWidth;
            document.getElementById('screen-width').textContent = width;
            
            let deviceType, printBehavior;
            if (width <= 480) {
                deviceType = 'Extra Small Mobile';
                printBehavior = 'Ultra compact (6px font) - ALL columns visible';
            } else if (width <= 768) {
                deviceType = 'Mobile';
                printBehavior = 'Compact (7-8px font) - ALL columns visible';
            } else if (width <= 1024) {
                deviceType = 'Tablet';
                printBehavior = 'Normal (10px font) - unchanged';
            } else {
                deviceType = 'Desktop';
                printBehavior = 'Normal (10-11px font) - unchanged';
            }
            
            document.getElementById('device-type').textContent = deviceType;
            document.getElementById('print-behavior').textContent = printBehavior;
        }
        
        function testPrint() {
            updateDeviceInfo();
            setTimeout(() => window.print(), 100);
        }
        
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
    </script>
</body>
</html>
